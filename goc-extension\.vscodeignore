# Source files
src/**
.vscode/**
.vscode-test/**

# Build files
tsconfig.json
.eslintrc.js

# Dependencies
node_modules/**
**/node_modules/**

# Test files
test/**
**/*.test.ts
**/*.test.js

# Development files
*.map
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
docs/**

# Package files
package-lock.json
yarn.lock

# IDE files
.idea/**
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
logs/**

# Temporary files
tmp/**
temp/**

# Coverage
coverage/**
.nyc_output/**

# GOC Core source files (include only dist)
../goc-core/src/**
../goc-core/node_modules/**
../goc-core/test/**
../goc-core/tests/**
../goc-core/*.test.*
../goc-core/*.spec.*
../goc-core/coverage/**
../goc-core/.nyc_output/**
../goc-core/docs/**
../goc-core/examples/**
../goc-core/.git/**
../goc-core/.vscode/**
../goc-core/tsconfig.json
../goc-core/.eslintrc.*
../goc-core/jest.config.*
../goc-core/README.md
../goc-core/CHANGELOG.md
../goc-core/LICENSE*
../goc-core/package-lock.json
../goc-core/yarn.lock

# Exclude specific problematic files
**/esm.d.mts
**/*.d.mts
**/node_modules/@types/fs-extra/esm.d.mts
../goc-core/node_modules/@types/fs-extra/**
node_modules/@goc-agent/core/node_modules/@types/fs-extra/**
