"use strict";
/**
 * Enhanced Web Capabilities Module
 *
 * Advanced web research, content processing, and learning integration
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebResearcher = void 0;
const utils_1 = require("../utils");
/**
 * Enhanced Web Research Engine
 */
class WebResearcher {
    constructor() {
        this.searchEngines = new Map();
        this.learningEvents = [];
        this.initializeSearchEngines();
    }
    /**
     * Enhanced search with multiple engines and learning integration
     */
    async search(query, options = {}) {
        try {
            utils_1.logger.info('Starting enhanced web search', { query, options });
            // Record search event for learning
            await this.recordLearningEvent({
                type: 'search',
                query,
                timestamp: new Date(),
                metadata: { options }
            });
            // Use DuckDuckGo as primary search engine (privacy-focused)
            const results = await this.searchDuckDuckGo(query, options);
            // Enhance results with content if requested
            const enhancedResults = options.includeContent ?
                await this.enhanceWithContent(results) : results;
            // Extract learning patterns
            await this.extractSearchPatterns(query, enhancedResults);
            utils_1.logger.info('Enhanced web search completed', {
                query,
                resultsCount: enhancedResults.length
            });
            return enhancedResults;
        }
        catch (error) {
            utils_1.logger.error('Enhanced web search failed', error);
            return [];
        }
    }
    /**
     * Fetch and process content from URL
     */
    async fetchContent(url) {
        try {
            utils_1.logger.info('Fetching web content', { url });
            // Use fetch API with proper headers
            const response = await fetch(url, {
                headers: {
                    'User-Agent': 'GOC-Agent/1.0 (Web Research Bot)',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'en-US,en;q=0.5',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive',
                },
                signal: AbortSignal.timeout(10000)
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            const html = await response.text();
            const processed = await this.processHtmlContent(html, url);
            // Record content extraction event
            await this.recordLearningEvent({
                type: 'content_extraction',
                url,
                timestamp: new Date(),
                metadata: {
                    wordCount: processed.content.length,
                    title: processed.title
                }
            });
            utils_1.logger.info('Web content fetched successfully', {
                url,
                contentLength: processed.content.length
            });
            return processed;
        }
        catch (error) {
            utils_1.logger.error('Failed to fetch web content', error);
            throw error;
        }
    }
    /**
     * Enhanced content extraction with code examples and metadata
     */
    async extractContent(url, options = {}) {
        try {
            const webContent = await this.fetchContent(url);
            // Extract code examples if requested
            const codeExamples = options.extractCode ?
                this.extractCodeExamples(webContent.content) : [];
            // Generate summary if requested
            const summary = options.includeSummary ?
                await this.summarizeContent(webContent.content) : '';
            // Extract links
            const links = this.extractLinks(webContent.content, url);
            // Calculate metadata
            const metadata = {
                wordCount: webContent.content.split(/\s+/).length,
                readingTime: Math.ceil(webContent.content.split(/\s+/).length / 200), // ~200 WPM
                language: this.detectLanguage(webContent.content),
                publishedDate: this.extractPublishedDate(webContent.content),
                author: this.extractAuthor(webContent.content),
                tags: this.extractTags(webContent.content)
            };
            // Learn from code examples
            if (codeExamples.length > 0) {
                await this.learnFromCodeExamples(codeExamples, url);
            }
            return {
                title: webContent.title,
                content: webContent.content,
                summary,
                codeExamples,
                links,
                metadata
            };
        }
        catch (error) {
            utils_1.logger.error('Content extraction failed', error);
            throw error;
        }
    }
    /**
     * Enhanced content summarization
     */
    async summarizeContent(content) {
        try {
            // Simple extractive summarization - in production, this would use AI
            const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 20);
            // Score sentences based on word frequency and position
            const wordFreq = this.calculateWordFrequency(content);
            const scoredSentences = sentences.map((sentence, index) => {
                const words = sentence.toLowerCase().split(/\s+/);
                const score = words.reduce((sum, word) => sum + (wordFreq[word] || 0), 0) / words.length;
                const positionScore = index < sentences.length * 0.3 ? 1.2 : 1.0; // Boost early sentences
                return { sentence: sentence.trim(), score: score * positionScore };
            });
            // Select top sentences
            const topSentences = scoredSentences
                .sort((a, b) => b.score - a.score)
                .slice(0, 3)
                .map(s => s.sentence);
            return topSentences.join('. ') + '.';
        }
        catch (error) {
            utils_1.logger.error('Content summarization failed', error);
            return content.substring(0, 500) + '...';
        }
    }
    /**
     * Research a specific technology or topic
     */
    async researchTopic(topic, options = {}) {
        try {
            utils_1.logger.info('Starting topic research', { topic, options });
            const depth = options.depth || 'intermediate';
            const maxSources = options.maxSources || 10;
            // Generate search queries based on depth
            const queries = this.generateResearchQueries(topic, depth);
            // Search for information
            const allResults = [];
            for (const query of queries) {
                const results = await this.search(query, {
                    maxResults: Math.ceil(maxSources / queries.length),
                    includeContent: true
                });
                allResults.push(...results);
            }
            // Remove duplicates and rank by relevance
            const uniqueResults = this.deduplicateResults(allResults);
            const topResults = uniqueResults.slice(0, maxSources);
            // Extract content from top sources
            const contentResults = await Promise.all(topResults.slice(0, 5).map(result => this.extractContent(result.url, {
                includeSummary: true,
                extractCode: options.includeExamples,
                analyzePatterns: true
            }).catch(() => null)));
            const validContent = contentResults.filter(Boolean);
            // Generate summary and key points
            const summary = await this.generateTopicSummary(topic, validContent);
            const keyPoints = await this.extractKeyPoints(validContent);
            // Collect code examples
            const codeExamples = validContent.flatMap(content => content.codeExamples.map(example => ({
                language: example.language,
                code: example.code,
                description: example.description || `${example.language} code example`,
                source: content.title
            })));
            // Get trend analysis if requested
            let trends = [];
            if (options.includeTrends) {
                trends = await this.analyzeTrends(topic);
            }
            // Extract related topics
            const relatedTopics = await this.extractRelatedTopics(validContent);
            // Record research event
            await this.recordLearningEvent({
                type: 'pattern_learning',
                query: topic,
                timestamp: new Date(),
                patterns: keyPoints,
                metadata: {
                    depth,
                    sourcesAnalyzed: validContent.length,
                    codeExamples: codeExamples.length
                }
            });
            utils_1.logger.info('Topic research completed', {
                topic,
                sourcesAnalyzed: validContent.length,
                keyPoints: keyPoints.length,
                codeExamples: codeExamples.length
            });
            return {
                summary,
                keyPoints,
                codeExamples,
                trends,
                sources: topResults,
                relatedTopics
            };
        }
        catch (error) {
            utils_1.logger.error('Topic research failed', error);
            throw error;
        }
    }
    /**
     * Get learning insights and recommendations
     */
    async getLearningInsights() {
        try {
            // Analyze search patterns
            const searchCounts = this.learningEvents
                .filter(e => e.type === 'search' && e.query)
                .reduce((acc, event) => {
                const query = event.query;
                acc[query] = (acc[query] || 0) + 1;
                return acc;
            }, {});
            const topSearches = Object.entries(searchCounts)
                .sort(([, a], [, b]) => b - a)
                .slice(0, 10)
                .map(([query, count]) => ({ query, count }));
            // Analyze discovered patterns
            const patternCounts = this.learningEvents
                .filter(e => e.type === 'pattern_learning' && e.patterns)
                .flatMap(e => e.patterns)
                .reduce((acc, pattern) => {
                acc[pattern] = (acc[pattern] || 0) + 1;
                return acc;
            }, {});
            const discoveredPatterns = Object.entries(patternCounts)
                .sort(([, a], [, b]) => b - a)
                .slice(0, 20)
                .map(([pattern, frequency]) => ({
                pattern,
                frequency,
                sources: ['web-research'] // Simplified
            }));
            // Generate recommendations based on patterns
            const recommendedTopics = this.generateRecommendations(topSearches, discoveredPatterns);
            // Extract trending technologies
            const trendingTechnologies = this.extractTrendingTechnologies(discoveredPatterns);
            return {
                topSearches,
                discoveredPatterns,
                recommendedTopics,
                trendingTechnologies
            };
        }
        catch (error) {
            utils_1.logger.error('Failed to get learning insights', error);
            return {
                topSearches: [],
                discoveredPatterns: [],
                recommendedTopics: [],
                trendingTechnologies: []
            };
        }
    }
    // Private helper methods
    initializeSearchEngines() {
        // Initialize search engines - DuckDuckGo as primary for privacy
        this.searchEngines.set('duckduckgo', {
            baseUrl: 'https://api.duckduckgo.com/',
            apiKey: null // DuckDuckGo doesn't require API key
        });
    }
    async searchDuckDuckGo(query, options) {
        try {
            // DuckDuckGo Instant Answer API
            const params = new URLSearchParams({
                q: query,
                format: 'json',
                no_html: '1',
                skip_disambig: '1'
            });
            const response = await fetch(`https://api.duckduckgo.com/?${params}`);
            const data = await response.json();
            const results = [];
            // Process main result
            if (data.Abstract) {
                results.push({
                    title: data.Heading || query,
                    url: data.AbstractURL || '',
                    snippet: data.Abstract,
                    relevanceScore: 0.9,
                    source: 'duckduckgo',
                    metadata: {
                        type: data.Type,
                        definition: data.Definition
                    }
                });
            }
            // Process related topics
            if (data.RelatedTopics) {
                data.RelatedTopics.slice(0, options.maxResults || 10).forEach((topic, index) => {
                    if (topic.Text && topic.FirstURL) {
                        results.push({
                            title: topic.Text.split(' - ')[0] || topic.Text,
                            url: topic.FirstURL,
                            snippet: topic.Text,
                            relevanceScore: 0.8 - (index * 0.1),
                            source: 'duckduckgo',
                            metadata: {
                                icon: topic.Icon
                            }
                        });
                    }
                });
            }
            // If no results from DuckDuckGo API, fall back to web scraping (simplified)
            if (results.length === 0) {
                return await this.fallbackSearch(query, options);
            }
            return results;
        }
        catch (error) {
            utils_1.logger.error('DuckDuckGo search failed', error);
            return await this.fallbackSearch(query, options);
        }
    }
    async fallbackSearch(query, options) {
        // Simplified fallback search - in production, this would use multiple search APIs
        const mockResults = [
            {
                title: `${query} - Documentation`,
                url: `https://example.com/docs/${encodeURIComponent(query)}`,
                snippet: `Official documentation and guides for ${query}`,
                relevanceScore: 0.9,
                source: 'fallback'
            },
            {
                title: `${query} - Tutorial`,
                url: `https://example.com/tutorial/${encodeURIComponent(query)}`,
                snippet: `Learn ${query} with step-by-step tutorials and examples`,
                relevanceScore: 0.8,
                source: 'fallback'
            },
            {
                title: `${query} - Best Practices`,
                url: `https://example.com/best-practices/${encodeURIComponent(query)}`,
                snippet: `Best practices and patterns for ${query} development`,
                relevanceScore: 0.7,
                source: 'fallback'
            }
        ];
        return mockResults.slice(0, options.maxResults || 10);
    }
    async enhanceWithContent(results) {
        return await Promise.all(results.map(async (result) => {
            try {
                const content = await this.fetchContent(result.url);
                return {
                    ...result,
                    content: content.content
                };
            }
            catch {
                return result; // Return original if content fetch fails
            }
        }));
    }
    async processHtmlContent(html, url) {
        // Simple HTML processing - in production, this would use a proper HTML parser
        const title = this.extractTitle(html);
        const content = this.extractTextContent(html);
        return {
            title,
            content,
            url,
            timestamp: new Date(),
            metadata: {
                contentLength: content.length,
                extractedAt: new Date()
            }
        };
    }
    extractTitle(html) {
        const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
        return titleMatch ? titleMatch[1].trim() : 'Untitled';
    }
    extractTextContent(html) {
        // Remove scripts, styles, and other non-content elements
        let content = html
            .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
            .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '')
            .replace(/<[^>]+>/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();
        return content;
    }
    extractCodeExamples(content) {
        const codeExamples = [];
        // Look for common code patterns
        const patterns = [
            { language: 'javascript', pattern: /```(?:js|javascript)\n([\s\S]*?)```/gi },
            { language: 'typescript', pattern: /```(?:ts|typescript)\n([\s\S]*?)```/gi },
            { language: 'python', pattern: /```(?:py|python)\n([\s\S]*?)```/gi },
            { language: 'php', pattern: /```php\n([\s\S]*?)```/gi },
            { language: 'java', pattern: /```java\n([\s\S]*?)```/gi },
            { language: 'css', pattern: /```css\n([\s\S]*?)```/gi },
            { language: 'html', pattern: /```html\n([\s\S]*?)```/gi },
            { language: 'sql', pattern: /```sql\n([\s\S]*?)```/gi }
        ];
        patterns.forEach(({ language, pattern }) => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                codeExamples.push({
                    language,
                    code: match[1].trim(),
                    description: `${language} code example`,
                    source: 'web-research'
                });
            }
        });
        return codeExamples;
    }
    extractLinks(content, baseUrl) {
        const links = [];
        const linkPattern = /<a[^>]+href=["']([^"']+)["'][^>]*>([^<]+)<\/a>/gi;
        let match;
        while ((match = linkPattern.exec(content)) !== null) {
            const url = match[1];
            const title = match[2].trim();
            const isInternal = url.startsWith('/') || url.includes(new URL(baseUrl).hostname);
            links.push({
                url: url.startsWith('/') ? new URL(url, baseUrl).href : url,
                title,
                type: isInternal ? 'internal' : 'external'
            });
        }
        return links.slice(0, 20); // Limit to 20 links
    }
    detectLanguage(content) {
        // Simple language detection based on common words
        const englishWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
        const words = content.toLowerCase().split(/\s+/).slice(0, 100);
        const englishCount = words.filter(word => englishWords.includes(word)).length;
        return englishCount > words.length * 0.1 ? 'en' : 'unknown';
    }
    extractPublishedDate(content) {
        // Look for common date patterns
        const datePatterns = [
            /published[:\s]+(\d{4}-\d{2}-\d{2})/i,
            /date[:\s]+(\d{4}-\d{2}-\d{2})/i,
            /(\d{1,2}\/\d{1,2}\/\d{4})/,
            /(\d{4}-\d{2}-\d{2})/
        ];
        for (const pattern of datePatterns) {
            const match = content.match(pattern);
            if (match) {
                const date = new Date(match[1]);
                if (!isNaN(date.getTime())) {
                    return date;
                }
            }
        }
        return undefined;
    }
    extractAuthor(content) {
        // Look for author patterns
        const authorPatterns = [
            /author[:\s]+([^<\n]+)/i,
            /by[:\s]+([^<\n]+)/i,
            /written by[:\s]+([^<\n]+)/i
        ];
        for (const pattern of authorPatterns) {
            const match = content.match(pattern);
            if (match) {
                return match[1].trim();
            }
        }
        return undefined;
    }
    extractTags(content) {
        // Extract tags from meta keywords or common tag patterns
        const tagPatterns = [
            /keywords[:\s]+([^<\n]+)/i,
            /tags[:\s]+([^<\n]+)/i,
            /#(\w+)/g
        ];
        const tags = [];
        tagPatterns.forEach(pattern => {
            const matches = content.match(pattern);
            if (matches) {
                if (pattern.global) {
                    matches.forEach(match => {
                        const tag = match.replace('#', '');
                        if (tag.length > 2)
                            tags.push(tag);
                    });
                }
                else {
                    const tagList = matches[1].split(/[,;]/).map(tag => tag.trim());
                    tags.push(...tagList);
                }
            }
        });
        return [...new Set(tags)].slice(0, 10);
    }
    calculateWordFrequency(content) {
        const words = content.toLowerCase()
            .replace(/[^\w\s]/g, '')
            .split(/\s+/)
            .filter(word => word.length > 3);
        const frequency = {};
        words.forEach(word => {
            frequency[word] = (frequency[word] || 0) + 1;
        });
        return frequency;
    }
    async recordLearningEvent(event) {
        this.learningEvents.push(event);
        // Keep only recent events (last 1000)
        if (this.learningEvents.length > 1000) {
            this.learningEvents = this.learningEvents.slice(-1000);
        }
        utils_1.logger.debug('Learning event recorded', { type: event.type, timestamp: event.timestamp });
    }
    async extractSearchPatterns(query, results) {
        const patterns = results.map(result => result.title.toLowerCase())
            .join(' ')
            .split(' ')
            .filter(word => word.length > 3)
            .reduce((acc, word) => {
            acc[word] = (acc[word] || 0) + 1;
            return acc;
        }, {});
        const topPatterns = Object.entries(patterns)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 10)
            .map(([pattern]) => pattern);
        if (topPatterns.length > 0) {
            await this.recordLearningEvent({
                type: 'pattern_learning',
                query,
                timestamp: new Date(),
                patterns: topPatterns
            });
        }
    }
    async learnFromCodeExamples(codeExamples, source) {
        for (const example of codeExamples) {
            await this.recordLearningEvent({
                type: 'code_discovery',
                timestamp: new Date(),
                content: example.code,
                metadata: {
                    language: example.language,
                    description: example.description,
                    source
                }
            });
        }
    }
    generateResearchQueries(topic, depth) {
        const baseQueries = [
            `${topic} tutorial`,
            `${topic} best practices`,
            `${topic} examples`
        ];
        const depthQueries = {
            basic: [
                `what is ${topic}`,
                `${topic} introduction`,
                `${topic} getting started`
            ],
            intermediate: [
                `${topic} advanced features`,
                `${topic} patterns`,
                `${topic} architecture`
            ],
            advanced: [
                `${topic} performance optimization`,
                `${topic} enterprise patterns`,
                `${topic} scalability`
            ]
        };
        return [...baseQueries, ...depthQueries[depth]];
    }
    deduplicateResults(results) {
        const seen = new Set();
        return results.filter(result => {
            const key = result.url.toLowerCase();
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }
    async generateTopicSummary(topic, content) {
        const allContent = content.map(c => c.summary || c.content.substring(0, 500)).join(' ');
        const sentences = allContent.split('.').filter(s => s.trim().length > 20);
        return sentences.slice(0, 3).join('. ') + '.';
    }
    async extractKeyPoints(content) {
        const allText = content.map(c => c.content).join(' ');
        const commonPhrases = allText.toLowerCase()
            .split(/[.!?]/)
            .filter(sentence => sentence.length > 20 && sentence.length < 200)
            .slice(0, 10);
        return commonPhrases.map(phrase => phrase.trim()).filter(Boolean);
    }
    async analyzeTrends(topic) {
        // Simplified trend analysis - in production, this would use real trend data
        return [
            {
                topic,
                trend: 'rising',
                confidence: 0.8,
                relatedTopics: [`${topic} framework`, `${topic} library`, `${topic} tools`],
                timeframe: 'last 30 days',
                sources: ['web-research']
            }
        ];
    }
    async extractRelatedTopics(content) {
        const allTags = content.flatMap(c => c.metadata.tags || []);
        const uniqueTags = [...new Set(allTags)];
        return uniqueTags.slice(0, 10);
    }
    generateRecommendations(topSearches, patterns) {
        const searchTopics = topSearches.map(s => s.query);
        const patternTopics = patterns.map(p => p.pattern);
        return [...new Set([...searchTopics, ...patternTopics])].slice(0, 10);
    }
    extractTrendingTechnologies(patterns) {
        const techKeywords = ['javascript', 'python', 'react', 'vue', 'angular', 'node', 'typescript', 'php', 'laravel', 'docker', 'kubernetes'];
        return patterns
            .filter(p => techKeywords.some(tech => p.pattern.includes(tech)))
            .map(p => p.pattern)
            .slice(0, 10);
    }
}
exports.WebResearcher = WebResearcher;
__exportStar(require("../types"), exports);
//# sourceMappingURL=index.js.map