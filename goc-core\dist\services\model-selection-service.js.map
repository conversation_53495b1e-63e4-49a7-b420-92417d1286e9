{"version": 3, "file": "model-selection-service.js", "sourceRoot": "", "sources": ["../../src/services/model-selection-service.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAGH,6DAA4D;AAmE5D,MAAa,qBAAqB;IAIhC;QAFQ,oBAAe,GAAY,KAAK,CAAC;QAGvC,IAAI,CAAC,MAAM,GAAG,IAAA,iCAAgB,GAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,UAAiC,EAAE;QACzD,MAAM,EACJ,YAAY,GAAG,IAAI,EACnB,aAAa,GAAG,IAAI,EACpB,YAAY,GAAG,KAAK,EACpB,QAAQ,GAAG,MAAM,EAClB,GAAG,OAAO,CAAC;QAEZ,MAAM,SAAS,GAAmB,EAAE,CAAC;QAErC,mCAAmC;QACnC,IAAI,YAAY,EAAE,CAAC;YACjB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACtD,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACjC,CAAC;QAED,sCAAsC;QACtC,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACxD,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAC9B,CAAC;QAED,MAAM,eAAe,GAAyB;YAC5C,KAAK,EAAE,aAAa;YACpB,SAAS,EAAE,iBAAiB;YAC5B,SAAS,EAAE,eAAe;SAC3B,CAAC;QAEF,OAAO;YACL,SAAS;YACT,eAAe;SAChB,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,iBAAiB;QAC7B,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;QACrD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAEzD,IAAI,MAAM,GAAgB,EAAE,CAAC;QAE7B,IAAI,WAAW,EAAE,CAAC;YAChB,mCAAmC;YACnC,MAAM,GAAG,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;QACjD,CAAC;aAAM,CAAC;YACN,yCAAyC;YACzC,MAAM,GAAG,YAAY,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;gBACzD,EAAE,EAAE,KAAK,CAAC,EAAE;gBACZ,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,IAAI,EAAE,MAAe;gBACrB,WAAW,EAAE,KAAK,CAAC,WAAW;gBAC9B,YAAY,EAAE,KAAK,CAAC,YAAY;gBAChC,aAAa,EAAE,KAAK,CAAC,aAAa;gBAClC,WAAW,EAAE,KAAK;gBAClB,YAAY,EAAE,KAAK;gBACnB,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,KAAK,CAAC,IAAI;gBAChB,cAAc,EAAE,KAAK,CAAC,cAAc;aACrC,CAAC,CAAC,CAAC;QACN,CAAC;QAED,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,YAAY,CAAC,WAAW;YACrC,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,IAAI;YACb,YAAY,EAAE,KAAK;YACnB,WAAW;YACX,MAAM;YACN,iBAAiB,EAAE,YAAY,CAAC,iBAAiB;SAClD,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,cAAc,CAAC,QAAyB;QACpD,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC;QAE/C,MAAM,MAAM,GAAgB,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;YAChE,EAAE,EAAE,KAAK,CAAC,EAAE;YACZ,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,WAAW,EAAE,KAAK,CAAC,WAAW;YAC9B,YAAY,EAAE,KAAK,CAAC,YAAY;YAChC,aAAa,EAAE,KAAK,CAAC,aAAa;YAClC,WAAW,EAAE,IAAI;YACjB,YAAY,EAAE,IAAI;YAClB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,KAAK,CAAC,OAAO;YACtB,MAAM,EAAE,KAAK,CAAC,MAAM;SACrB,CAAC,CAAC,CAAC;QAEJ,OAAO;YACL,IAAI,EAAE,KAAK;YACX,WAAW,EAAE,SAAS,CAAC,WAAW;YAClC,IAAI,EAAE,UAAU;YAChB,OAAO,EAAE,KAAK;YACd,YAAY,EAAE,IAAI;YAClB,WAAW,EAAE,IAAI;YACjB,MAAM;YACN,iBAAiB,EAAE,SAAS,CAAC,iBAAiB;YAC9C,iBAAiB,EAAE,SAAS,CAAC,iBAAiB;SAC/C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB;QAC3B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,iCAAiC,EAAE;gBAC9D,MAAM,EAAE,KAAK;gBACb,MAAM,EAAE,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC;aAClC,CAAC,CAAC;YACH,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC,EAAE,CAAC;YACnC,OAAO,QAAQ,CAAC,EAAE,CAAC;QACrB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;YAC7B,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,wBAAwB;QAC5B,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,iCAAiC,CAAC,CAAC;YAChE,IAAI,CAAC,QAAQ,CAAC,EAAE;gBAAE,OAAO,EAAE,CAAC;YAE5B,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAS,CAAC;YAC1C,MAAM,eAAe,GAAG,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;YAE1C,qCAAqC;YACrC,OAAO,eAAe,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,CAAC;gBAC1C,EAAE,EAAE,KAAK,CAAC,IAAI;gBACd,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC;gBACtC,IAAI,EAAE,MAAe;gBACrB,WAAW,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC;gBACjD,YAAY,EAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC;gBACnD,aAAa,EAAE,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,IAAI,CAAC;gBACrD,WAAW,EAAE,IAAI;gBACjB,YAAY,EAAE,KAAK;gBACnB,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC;aAClC,CAAC,CAAC,CAAC;QACN,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,OAAe;QACrC,MAAM,OAAO,GAA2B;YACtC,aAAa,EAAE,cAAc;YAC7B,aAAa,EAAE,cAAc;YAC7B,cAAc,EAAE,eAAe;YAC/B,eAAe,EAAE,gBAAgB;YACjC,YAAY,EAAE,YAAY;YAC1B,qBAAqB,EAAE,qBAAqB;YAC5C,kBAAkB,EAAE,kBAAkB;SACvC,CAAC;QAEF,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC;IACrC,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,OAAe;QACzC,MAAM,cAAc,GAA2B;YAC7C,aAAa,EAAE,0CAA0C;YACzD,aAAa,EAAE,mCAAmC;YAClD,cAAc,EAAE,iCAAiC;YACjD,eAAe,EAAE,uCAAuC;YACxD,YAAY,EAAE,uBAAuB;YACrC,qBAAqB,EAAE,4BAA4B;YACnD,kBAAkB,EAAE,qBAAqB;SAC1C,CAAC;QAEF,OAAO,cAAc,CAAC,OAAO,CAAC,IAAI,gBAAgB,CAAC;IACrD,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,OAAe;QAC1C,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC7B,OAAO,CAAC,iBAAiB,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;QAC7D,CAAC;QACD,OAAO,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,OAAe;QAC3C,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC;YAAE,OAAO,KAAK,CAAC;QAChD,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACK,UAAU,CAAC,KAAa;QAC9B,MAAM,EAAE,GAAG,KAAK,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;QACxC,OAAO,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,sBAAsB,CAAC,UAAsB,EAAE,OAAe;QAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACtD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,YAAY,UAAU,YAAY;gBACzC,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;QAED,OAAO;YACL,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;IACJ,CAAC;CACF;AAnPD,sDAmPC"}