/**
 * Package Manager Tool
 *
 * Multi-platform package manager detection, dependency installation, and management
 */
import { ToolResult } from './index';
export interface PackageManagerInterface {
    name: string;
    command: string;
    configFile: string;
    lockFile?: string;
    installCommand: string[];
    uninstallCommand: string[];
    updateCommand: string[];
    listCommand: string[];
}
export interface PackageInfo {
    name: string;
    version: string;
    description?: string;
    dependencies?: Record<string, string>;
    devDependencies?: Record<string, string>;
}
export interface DependencyInfo {
    name: string;
    version: string;
    type: 'dependency' | 'devDependency' | 'peerDependency';
    installed: boolean;
    latest?: string;
}
export declare class PackageManager {
    private static managers;
    /**
     * Detect available package managers in a directory
     */
    detectPackageManagers(projectPath?: string): Promise<ToolResult>;
    /**
     * Install packages using the appropriate package manager
     */
    installPackages(packages: string[], options?: {
        dev?: boolean;
        global?: boolean;
        manager?: string;
        projectPath?: string;
    }): Promise<ToolResult>;
    /**
     * Uninstall packages
     */
    uninstallPackages(packages: string[], options?: {
        manager?: string;
        projectPath?: string;
    }): Promise<ToolResult>;
    /**
     * Update packages
     */
    updatePackages(packages?: string[], options?: {
        manager?: string;
        projectPath?: string;
    }): Promise<ToolResult>;
    /**
     * List installed packages
     */
    listPackages(options?: {
        manager?: string;
        projectPath?: string;
    }): Promise<ToolResult>;
    /**
     * Check for security vulnerabilities
     */
    auditPackages(options?: {
        manager?: string;
        projectPath?: string;
        fix?: boolean;
    }): Promise<ToolResult>;
    private checkCommandAvailable;
    private getManagerVersion;
    private getPreferredManager;
    private getRecommendedManager;
    private parseNpmYarnList;
    private parsePipList;
    private executeCommand;
}
//# sourceMappingURL=package-manager.d.ts.map