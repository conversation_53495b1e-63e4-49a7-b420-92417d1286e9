/**
 * Package Manager Tool
 * 
 * Multi-platform package manager detection, dependency installation, and management
 */

import * as fs from 'fs';
import * as path from 'path';
import { spawn } from 'child_process';
import { ToolResult } from './index';

export interface PackageManagerInterface {
  name: string;
  command: string;
  configFile: string;
  lockFile?: string;
  installCommand: string[];
  uninstallCommand: string[];
  updateCommand: string[];
  listCommand: string[];
}

export interface PackageInfo {
  name: string;
  version: string;
  description?: string;
  dependencies?: Record<string, string>;
  devDependencies?: Record<string, string>;
}

export interface DependencyInfo {
  name: string;
  version: string;
  type: 'dependency' | 'devDependency' | 'peerDependency';
  installed: boolean;
  latest?: string;
}

interface ManagerConfig {
  name: string;
  command: string;
  configFile: string;
  lockFile?: string;
  installCommand: string[];
  uninstallCommand: string[];
  updateCommand: string[];
  listCommand: string[];
}

export class PackageManager {
  private static managers: Record<string, ManagerConfig> = {
    npm: {
      name: 'npm',
      command: 'npm',
      configFile: 'package.json',
      lockFile: 'package-lock.json',
      installCommand: ['install'],
      uninstallCommand: ['uninstall'],
      updateCommand: ['update'],
      listCommand: ['list', '--json']
    },
    yarn: {
      name: 'yarn',
      command: 'yarn',
      configFile: 'package.json',
      lockFile: 'yarn.lock',
      installCommand: ['add'],
      uninstallCommand: ['remove'],
      updateCommand: ['upgrade'],
      listCommand: ['list', '--json']
    },
    pnpm: {
      name: 'pnpm',
      command: 'pnpm',
      configFile: 'package.json',
      lockFile: 'pnpm-lock.yaml',
      installCommand: ['add'],
      uninstallCommand: ['remove'],
      updateCommand: ['update'],
      listCommand: ['list', '--json']
    },
    pip: {
      name: 'pip',
      command: 'pip',
      configFile: 'requirements.txt',
      installCommand: ['install'],
      uninstallCommand: ['uninstall'],
      updateCommand: ['install', '--upgrade'],
      listCommand: ['list', '--format=json']
    },
    poetry: {
      name: 'poetry',
      command: 'poetry',
      configFile: 'pyproject.toml',
      lockFile: 'poetry.lock',
      installCommand: ['add'],
      uninstallCommand: ['remove'],
      updateCommand: ['update'],
      listCommand: ['show', '--tree']
    },
    cargo: {
      name: 'cargo',
      command: 'cargo',
      configFile: 'Cargo.toml',
      lockFile: 'Cargo.lock',
      installCommand: ['add'],
      uninstallCommand: ['remove'],
      updateCommand: ['update'],
      listCommand: ['tree']
    },
    composer: {
      name: 'composer',
      command: 'composer',
      configFile: 'composer.json',
      lockFile: 'composer.lock',
      installCommand: ['require'],
      uninstallCommand: ['remove'],
      updateCommand: ['update'],
      listCommand: ['show', '--format=json']
    },
    go: {
      name: 'go',
      command: 'go',
      configFile: 'go.mod',
      installCommand: ['get'],
      uninstallCommand: ['mod', 'edit', '-droprequire'],
      updateCommand: ['get', '-u'],
      listCommand: ['list', '-m', 'all']
    }
  };

  /**
   * Detect available package managers in a directory
   */
  async detectPackageManagers(projectPath: string = process.cwd()): Promise<ToolResult> {
    try {
      const detected: Array<{ manager: ManagerConfig; configPath: string; lockPath?: string }> = [];

      for (const [key, manager] of Object.entries(PackageManager.managers)) {
        const configPath = path.join(projectPath, manager.configFile);
        
        if (fs.existsSync(configPath)) {
          const lockPath = manager.lockFile ? path.join(projectPath, manager.lockFile) : undefined;
          const hasLock = lockPath ? fs.existsSync(lockPath) : false;
          
          detected.push({
            manager,
            configPath,
            lockPath: hasLock ? lockPath : undefined
          });
        }
      }

      // Check if package manager commands are available
      const available: Array<{ manager: ManagerConfig; available: boolean; version?: string }> = [];
      
      for (const detection of detected) {
        const isAvailable = await this.checkCommandAvailable(detection.manager.command);
        const version = isAvailable ? await this.getManagerVersion(detection.manager.command) : undefined;
        
        available.push({
          manager: detection.manager,
          available: isAvailable,
          version
        });
      }

      return {
        success: true,
        data: {
          detected: detected.map(d => d.manager.name),
          available: available.filter(a => a.available).map(a => ({
            name: a.manager.name,
            version: a.version
          })),
          recommended: this.getRecommendedManager(available.filter(a => a.available).map(a => a.manager))
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Install packages using the appropriate package manager
   */
  async installPackages(packages: string[], options: {
    dev?: boolean;
    global?: boolean;
    manager?: string;
    projectPath?: string;
  } = {}): Promise<ToolResult> {
    try {
      const projectPath = options.projectPath || process.cwd();
      const managerName = options.manager || await this.getPreferredManager(projectPath);
      const manager = PackageManager.managers[managerName];

      if (!manager) {
        return {
          success: false,
          error: `Package manager not supported: ${managerName}`
        };
      }

      const isAvailable = await this.checkCommandAvailable(manager.command);
      if (!isAvailable) {
        return {
          success: false,
          error: `Package manager not available: ${manager.command}`
        };
      }

      const args = [...manager.installCommand];
      
      // Add packages
      args.push(...packages);
      
      // Add flags
      if (options.dev && manager.name === 'npm') {
        args.push('--save-dev');
      } else if (options.dev && manager.name === 'yarn') {
        args.push('--dev');
      }
      
      if (options.global && ['npm', 'yarn', 'pnpm'].includes(manager.name)) {
        args.push('--global');
      }

      const result = await this.executeCommand(manager.command, args, projectPath);
      
      return {
        success: result.exitCode === 0,
        data: {
          manager: manager.name,
          packages,
          output: result.stdout,
          error: result.stderr,
          exitCode: result.exitCode
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Uninstall packages
   */
  async uninstallPackages(packages: string[], options: {
    manager?: string;
    projectPath?: string;
  } = {}): Promise<ToolResult> {
    try {
      const projectPath = options.projectPath || process.cwd();
      const managerName = options.manager || await this.getPreferredManager(projectPath);
      const manager = PackageManager.managers[managerName];

      if (!manager) {
        return {
          success: false,
          error: `Package manager not supported: ${managerName}`
        };
      }

      const args = [...manager.uninstallCommand, ...packages];
      const result = await this.executeCommand(manager.command, args, projectPath);
      
      return {
        success: result.exitCode === 0,
        data: {
          manager: manager.name,
          packages,
          output: result.stdout,
          error: result.stderr,
          exitCode: result.exitCode
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Update packages
   */
  async updatePackages(packages?: string[], options: {
    manager?: string;
    projectPath?: string;
  } = {}): Promise<ToolResult> {
    try {
      const projectPath = options.projectPath || process.cwd();
      const managerName = options.manager || await this.getPreferredManager(projectPath);
      const manager = PackageManager.managers[managerName];

      if (!manager) {
        return {
          success: false,
          error: `Package manager not supported: ${managerName}`
        };
      }

      const args = [...manager.updateCommand];
      if (packages && packages.length > 0) {
        args.push(...packages);
      }

      const result = await this.executeCommand(manager.command, args, projectPath);
      
      return {
        success: result.exitCode === 0,
        data: {
          manager: manager.name,
          packages: packages || ['all'],
          output: result.stdout,
          error: result.stderr,
          exitCode: result.exitCode
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * List installed packages
   */
  async listPackages(options: {
    manager?: string;
    projectPath?: string;
  } = {}): Promise<ToolResult> {
    try {
      const projectPath = options.projectPath || process.cwd();
      const managerName = options.manager || await this.getPreferredManager(projectPath);
      const manager = PackageManager.managers[managerName];

      if (!manager) {
        return {
          success: false,
          error: `Package manager not supported: ${managerName}`
        };
      }

      const result = await this.executeCommand(manager.command, manager.listCommand, projectPath);
      
      let packages: DependencyInfo[] = [];
      
      try {
        if (manager.name === 'npm' || manager.name === 'yarn') {
          const data = JSON.parse(result.stdout);
          packages = this.parseNpmYarnList(data);
        } else if (manager.name === 'pip') {
          const data = JSON.parse(result.stdout);
          packages = this.parsePipList(data);
        }
      } catch (parseError) {
        // Fallback to raw output if JSON parsing fails
      }
      
      return {
        success: result.exitCode === 0,
        data: {
          manager: manager.name,
          packages,
          rawOutput: result.stdout,
          error: result.stderr,
          exitCode: result.exitCode
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Check for security vulnerabilities
   */
  async auditPackages(options: {
    manager?: string;
    projectPath?: string;
    fix?: boolean;
  } = {}): Promise<ToolResult> {
    try {
      const projectPath = options.projectPath || process.cwd();
      const managerName = options.manager || await this.getPreferredManager(projectPath);
      
      let command: string;
      let args: string[];
      
      switch (managerName) {
        case 'npm':
          command = 'npm';
          args = options.fix ? ['audit', 'fix'] : ['audit'];
          break;
        case 'yarn':
          command = 'yarn';
          args = ['audit'];
          break;
        case 'pnpm':
          command = 'pnpm';
          args = ['audit'];
          break;
        default:
          return {
            success: false,
            error: `Audit not supported for ${managerName}`
          };
      }

      const result = await this.executeCommand(command, args, projectPath);
      
      return {
        success: result.exitCode === 0,
        data: {
          manager: managerName,
          output: result.stdout,
          error: result.stderr,
          exitCode: result.exitCode,
          hasVulnerabilities: result.exitCode !== 0
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // Helper methods
  private async checkCommandAvailable(command: string): Promise<boolean> {
    try {
      const result = await this.executeCommand(command, ['--version'], process.cwd(), 5000);
      return result.exitCode === 0;
    } catch {
      return false;
    }
  }

  private async getManagerVersion(command: string): Promise<string | undefined> {
    try {
      const result = await this.executeCommand(command, ['--version'], process.cwd(), 5000);
      return result.exitCode === 0 ? result.stdout.trim() : undefined;
    } catch {
      return undefined;
    }
  }

  private async getPreferredManager(projectPath: string): Promise<string> {
    // Check for lock files to determine preferred manager
    if (fs.existsSync(path.join(projectPath, 'yarn.lock'))) return 'yarn';
    if (fs.existsSync(path.join(projectPath, 'pnpm-lock.yaml'))) return 'pnpm';
    if (fs.existsSync(path.join(projectPath, 'package-lock.json'))) return 'npm';
    if (fs.existsSync(path.join(projectPath, 'poetry.lock'))) return 'poetry';
    if (fs.existsSync(path.join(projectPath, 'Cargo.lock'))) return 'cargo';
    if (fs.existsSync(path.join(projectPath, 'composer.lock'))) return 'composer';
    if (fs.existsSync(path.join(projectPath, 'go.mod'))) return 'go';
    
    // Default fallbacks
    if (fs.existsSync(path.join(projectPath, 'package.json'))) return 'npm';
    if (fs.existsSync(path.join(projectPath, 'requirements.txt'))) return 'pip';
    if (fs.existsSync(path.join(projectPath, 'pyproject.toml'))) return 'poetry';
    
    return 'npm'; // Default
  }

  private getRecommendedManager(available: ManagerConfig[]): string | undefined {
    const priority = ['pnpm', 'yarn', 'npm', 'poetry', 'cargo', 'composer', 'go', 'pip'];
    
    for (const preferred of priority) {
      if (available.some(m => m.name === preferred)) {
        return preferred;
      }
    }
    
    return available[0]?.name;
  }

  private parseNpmYarnList(data: any): DependencyInfo[] {
    const packages: DependencyInfo[] = [];
    
    if (data.dependencies) {
      for (const [name, info] of Object.entries(data.dependencies as any)) {
        packages.push({
          name,
          version: (info as any).version || 'unknown',
          type: 'dependency',
          installed: true
        });
      }
    }
    
    return packages;
  }

  private parsePipList(data: any[]): DependencyInfo[] {
    return data.map(pkg => ({
      name: pkg.name,
      version: pkg.version,
      type: 'dependency' as const,
      installed: true
    }));
  }

  private executeCommand(command: string, args: string[], cwd: string, timeout: number = 30000): Promise<{
    stdout: string;
    stderr: string;
    exitCode: number;
  }> {
    return new Promise((resolve) => {
      const process = spawn(command, args, { cwd, stdio: 'pipe' });
      
      let stdout = '';
      let stderr = '';
      
      process.stdout?.on('data', (data) => {
        stdout += data.toString();
      });
      
      process.stderr?.on('data', (data) => {
        stderr += data.toString();
      });
      
      process.on('close', (code) => {
        resolve({
          stdout,
          stderr,
          exitCode: code || 0
        });
      });
      
      setTimeout(() => {
        process.kill();
        resolve({
          stdout,
          stderr,
          exitCode: -1
        });
      }, timeout);
    });
  }
}
