# GOC Agent CLI

🤖 **AI-powered coding assistant with multi-provider support**

GOC Agent CLI is a powerful command-line interface that brings AI-powered coding assistance directly to your terminal. It supports multiple AI providers including Ollama (local), OpenAI, Groq, and Gemini, with intelligent context awareness and autonomous learning capabilities.

## ✨ Features

- 🔄 **Multi-Provider Support** - Ollama, OpenAI, Groq, Gemini
- 🏠 **Local & Cloud Models** - Run locally with Ollama or use cloud APIs
- 🧠 **Context-Aware** - Understands your codebase and project structure
- 🎯 **Smart Code Generation** - Functions, classes, components, tests, and more
- 📚 **Auto-Documentation** - Generate comprehensive project documentation
- 🚀 **Deployment Assistance** - Analyze and deploy your projects
- 🔍 **Web Research** - Fetch and analyze web content for learning
- 📊 **Real-time Monitoring** - Track AI usage and performance
- 🎨 **Beautiful CLI** - Clean, intuitive command-line interface

## 🚀 Quick Start

### Prerequisites

- **Node.js** 18.0.0 or higher
- **npm** or **yarn**
- **Ollama** (optional, for local AI models)

### Installation

```bash
# Clone the repository
git clone https://github.com/your-org/goc-agent.git
cd goc-agent/goc-agent-cli

# Install dependencies
npm install

# Build the CLI
npm run build

# Install globally (optional)
npm link
```

### First-Time Setup

1. **Copy configuration file:**
   ```bash
   mkdir -p ~/.goc-agent
   cp config.example.yaml ~/.goc-agent/config.yaml
   ```

2. **Choose your AI provider:**

   **Option A: Local with Ollama (Recommended for beginners)**
   ```bash
   # Install Ollama
   curl -fsSL https://ollama.ai/install.sh | sh

   # Pull a model
   ollama pull llama3.2:3b

   # Set as default (already configured in example)
   goc models select ollama llama3.2:3b
   ```

   **Option B: Cloud providers**
   ```bash
   # Configure API keys in ~/.goc-agent/config.yaml
   # Then select your preferred provider
   goc models select openai gpt-4
   ```

3. **Verify installation:**
   ```bash
   goc models status
   ```

## 📖 Usage Guide

### Core Commands

#### 💬 Interactive Chat
```bash
# Start interactive chat session
goc chat

# Chat with specific context
goc chat --context ./src

# Chat with web research enabled
goc chat --research
```

#### 🤖 AI Agent Tasks
```bash
# Execute coding tasks
goc agent "create a REST API for user management"
goc agent "add error handling to my functions"
goc agent "optimize this database query" --file query.sql

# Generate specific code types
goc generate function "calculate fibonacci sequence"
goc generate class "UserRepository with CRUD operations"
goc generate test "test the authentication middleware"
```

#### 🔍 Code Analysis
```bash
# Analyze current project
goc analyze

# Analyze specific files/directories
goc analyze ./src/components
goc analyze --file ./app.js

# Get improvement suggestions
goc analyze --suggestions
```

#### 📚 Documentation Generation
```bash
# Generate project documentation
goc docs generate

# Generate API documentation
goc docs api

# Generate README for current project
goc docs readme

# Generate specific documentation types
goc docs --type api --output ./docs
```

#### 🚀 Deployment & DevOps
```bash
# Analyze deployment readiness
goc deploy analyze

# Generate deployment configuration
goc deploy generate --platform docker
goc deploy generate --platform kubernetes
goc deploy generate --platform vercel

# Get deployment recommendations
goc deploy plan
```

#### 🌐 Web Research & Learning
```bash
# Research technologies and best practices
goc research "Laravel 11 best practices"
goc research "React performance optimization"

# Train on specific technologies
goc train --tech "php,laravel,mysql"
goc train --url "https://laravel.com/docs"

# Web content analysis
goc web analyze "https://example.com"
goc web extract "https://github.com/user/repo"
```

#### ⚙️ Configuration & Models
```bash
# List available models
goc models list

# Select AI provider and model
goc models select ollama llama3.2:3b
goc models select openai gpt-4
goc models select groq llama-3.1-70b-versatile

# Check model status
goc models status

# Test model connection
goc models test

# Configure settings
goc config set provider ollama
goc config set model llama3.2:3b
goc config show
```

#### 🔧 Tools & Utilities
```bash
# Package management
goc tools packages install express typescript
goc tools packages audit
goc tools packages update

# Project scaffolding
goc tools scaffold react-app my-app
goc tools scaffold express-api my-api

# Code formatting and linting
goc tools format ./src
goc tools lint --fix
```

#### 📊 Monitoring & Sessions
```bash
# Monitor AI usage and performance
goc monitor start
goc monitor status
goc monitor logs

# Session management
goc session list
goc session save my-session
goc session load my-session
goc session delete old-session
```

### Advanced Usage

#### Context-Aware Operations
```bash
# Analyze entire project context
goc context scan

# Update context index
goc context update

# Search within context
goc context search "authentication logic"

# Show context statistics
goc context stats
```

#### Autonomous Mode
```bash
# Enable autonomous coding mode
goc auto start --task "implement user authentication"

# Monitor autonomous progress
goc auto status

# Stop autonomous mode
goc auto stop
```

#### Authentication (for cloud providers)
```bash
# Login to GOC Agent cloud services
goc auth login

# Check authentication status
goc auth status

# Logout
goc auth logout

# Manage API keys
goc auth keys list
goc auth keys add openai sk-...
```

## ⚙️ Configuration

### Configuration File Location
- **Linux/macOS**: `~/.goc-agent/config.yaml`
- **Windows**: `%USERPROFILE%\.goc-agent\config.yaml`

### Configuration Structure
```yaml
# Default AI provider
defaultProvider: ollama
defaultModel: llama3.2:3b

# API Keys (use environment variables for security)
apiKeys:
  openai: ${OPENAI_API_KEY}
  groq: ${GROQ_API_KEY}
  gemini: ${GEMINI_API_KEY}

# Backend configuration (for GOC Agent cloud)
backend:
  url: https://api.goc-agent.com
  apiKey: ${GOC_AGENT_API_KEY}

# Codebase scanning settings
codebase:
  excludePatterns:
    - node_modules/**
    - .git/**
    - dist/**
    - "*.log"
  includePatterns:
    - "**/*.ts"
    - "**/*.js"
    - "**/*.py"
    - "**/*.php"
  maxFileSize: 1048576  # 1MB
  maxFiles: 1000

# UI preferences
preferences:
  theme: dark
  verbose: false
  autoSave: true
```

### Environment Variables
```bash
# AI Provider API Keys
export OPENAI_API_KEY="sk-..."
export GROQ_API_KEY="gsk_..."
export GEMINI_API_KEY="AI..."

# GOC Agent Cloud (optional)
export GOC_AGENT_API_KEY="goc_..."

# Ollama Configuration (if not using defaults)
export OLLAMA_HOST="http://localhost:11434"
```

### Provider-Specific Setup

#### Ollama (Local AI)
```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Available models
ollama pull llama3.2:3b      # 3B parameters, fast
ollama pull llama3.2:8b      # 8B parameters, balanced
ollama pull llama3.1:70b     # 70B parameters, powerful
ollama pull codellama:7b     # Code-specialized model
ollama pull mistral:7b       # Alternative model

# Start Ollama service
ollama serve
```

#### OpenAI
```bash
# Get API key from: https://platform.openai.com/api-keys
# Available models: gpt-4, gpt-4-turbo, gpt-3.5-turbo
goc config set apiKeys.openai "sk-your-api-key"
```

#### Groq (Fast Inference)
```bash
# Get API key from: https://console.groq.com/keys
# Available models: llama-3.1-70b-versatile, mixtral-8x7b-32768
goc config set apiKeys.groq "gsk-your-api-key"
```

#### Google Gemini
```bash
# Get API key from: https://aistudio.google.com/app/apikey
# Available models: gemini-pro, gemini-pro-vision
goc config set apiKeys.gemini "AI-your-api-key"
```

## 🛠️ Development

### Project Structure
```
goc-agent-cli/
├── src/
│   ├── commands/          # CLI command implementations
│   ├── config/           # Configuration management
│   ├── context/          # Context engine integration
│   ├── tools/            # Utility tools and helpers
│   └── utils/            # Common utilities
├── services/             # External service clients
├── dist/                 # Compiled JavaScript output
├── config.example.yaml   # Configuration template
└── goc.js               # CLI entry point
```

### Available Scripts
```bash
# Development
npm run build              # Compile TypeScript
npm run dev               # Build and run
npm run start             # Run compiled version
npm run clean             # Clean build artifacts

# Training (if available)
npm run train             # Manual training
npm run train:daemon      # Start training daemon
npm run train:stop        # Stop training daemon
npm run train:status      # Check training status
```

### Building from Source
```bash
# Clone the repository
git clone https://github.com/your-org/goc-agent.git
cd goc-agent

# Install dependencies for core
cd goc-core
npm install
npm run build

# Install dependencies for CLI
cd ../goc-agent-cli
npm install
npm run build

# Link for global usage
npm link
```

### Contributing
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and test thoroughly
4. Commit your changes: `git commit -m 'Add amazing feature'`
5. Push to the branch: `git push origin feature/amazing-feature`
6. Open a Pull Request

## 📋 Examples

### Example 1: Create a REST API
```bash
# Generate a complete REST API with authentication
goc agent "Create a Node.js REST API with Express for user management including authentication, CRUD operations, and input validation"

# The AI will:
# - Create project structure
# - Set up Express server
# - Implement authentication middleware
# - Create user routes and controllers
# - Add input validation
# - Generate tests
```

### Example 2: Analyze and Improve Code
```bash
# Analyze your current project
goc analyze --suggestions

# Get specific improvements for a file
goc analyze --file ./src/auth.js --focus security

# Generate tests for existing code
goc generate test --file ./src/utils.js
```

### Example 3: Documentation Workflow
```bash
# Generate comprehensive project documentation
goc docs generate --type full --output ./docs

# Create API documentation from code
goc docs api --format markdown --include-examples

# Generate README with project overview
goc docs readme --include-setup --include-examples
```

### Example 4: Deployment Pipeline
```bash
# Analyze deployment readiness
goc deploy analyze

# Generate Docker configuration
goc deploy generate --platform docker --optimize

# Create Kubernetes manifests
goc deploy generate --platform kubernetes --namespace production
```

## 🔧 Troubleshooting

### Common Issues

#### Ollama Connection Issues
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# Start Ollama service
ollama serve

# Check available models
ollama list

# Pull missing models
ollama pull llama3.2:3b
```

#### API Key Issues
```bash
# Verify API key configuration
goc config show

# Test API key validity
goc models test

# Set API key directly
goc config set apiKeys.openai "sk-your-new-key"
```

#### Build Issues
```bash
# Clean and rebuild
npm run clean
npm install
npm run build

# Check Node.js version (requires 18+)
node --version

# Clear npm cache if needed
npm cache clean --force
```

#### Permission Issues
```bash
# Fix global installation permissions (Linux/macOS)
sudo chown -R $(whoami) $(npm config get prefix)/{lib/node_modules,bin,share}

# Or use npx instead of global install
npx goc-agent-cli chat
```

### Debug Mode
```bash
# Enable verbose logging
goc --verbose chat

# Check configuration
goc config show --debug

# Monitor real-time logs
goc monitor logs --follow
```

## 🚀 Performance Tips

### Optimize Context Scanning
```bash
# Update exclude patterns for large projects
goc config set codebase.excludePatterns "node_modules/**,dist/**,*.log"

# Limit file scanning
goc config set codebase.maxFiles 500
goc config set codebase.maxFileSize 512000  # 512KB
```

### Model Selection
- **Fast responses**: Use `llama3.2:3b` or `gpt-3.5-turbo`
- **Best quality**: Use `llama3.1:70b` or `gpt-4`
- **Code-specific**: Use `codellama:7b` for programming tasks
- **Balanced**: Use `llama3.2:8b` or `groq/llama-3.1-70b-versatile`

### Memory Management
```bash
# Clear old sessions
goc session clean --older-than 7d

# Optimize context cache
goc context optimize

# Monitor memory usage
goc monitor system
```

## 🔗 Integration

### VS Code Extension
```bash
# Install GOC Agent VS Code extension
code --install-extension goc-agent.goc-extension
```

### IDE Integration
```bash
# Generate IDE configuration
goc tools ide --type vscode
goc tools ide --type intellij
```

### CI/CD Integration
```bash
# GitHub Actions example
goc deploy generate --platform github-actions --output .github/workflows

# GitLab CI example
goc deploy generate --platform gitlab-ci --output .gitlab-ci.yml
```

## 📊 Monitoring & Analytics

### Usage Statistics
```bash
# View usage statistics
goc monitor stats

# Export usage data
goc monitor export --format json --output usage.json

# Set up monitoring dashboard
goc monitor dashboard --port 3000
```

### Performance Metrics
```bash
# Check response times
goc monitor performance

# Model comparison
goc monitor compare --models "llama3.2:3b,gpt-4"

# Resource usage
goc monitor resources
```

## 🤝 Community & Support

### Getting Help
- 📖 **Documentation**: [docs.goc-agent.com](https://docs.goc-agent.com)
- 💬 **Discord**: [discord.gg/goc-agent](https://discord.gg/goc-agent)
- 🐛 **Issues**: [GitHub Issues](https://github.com/your-org/goc-agent/issues)
- 📧 **Email**: <EMAIL>

### Contributing
- 🔧 **Development**: See [CONTRIBUTING.md](CONTRIBUTING.md)
- 🐛 **Bug Reports**: Use GitHub Issues
- 💡 **Feature Requests**: Use GitHub Discussions
- 📝 **Documentation**: Help improve our docs

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Ollama** - For making local AI accessible
- **OpenAI** - For pioneering conversational AI
- **Groq** - For ultra-fast inference
- **Google** - For Gemini AI capabilities
- **Community** - For feedback and contributions

---

**Made with ❤️ by the GOC Agent Team**

*Empowering developers with AI-powered coding assistance*
```
