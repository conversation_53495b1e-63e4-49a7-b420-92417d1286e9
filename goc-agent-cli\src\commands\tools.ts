/**
 * Tools Command
 * 
 * Expose the enhanced tool system through CLI commands
 */

import chalk from 'chalk';
import inquirer from 'inquirer';
import { Command } from 'commander';
import { ToolRegistry, initializeTools } from '../tools';

export class ToolsCommand {
  private registry: ToolRegistry;

  constructor() {
    this.registry = ToolRegistry.getInstance();
    initializeTools();
  }

  register(program: Command): void {
    const toolsCmd = program
      .command('tools')
      .description('Enhanced development tools');

    // File operations
    toolsCmd
      .command('view <filePath>')
      .description('View file content with optional search')
      .option('-r, --range <start:end>', 'Line range to view (e.g., 1:50)')
      .option('-s, --search <regex>', 'Search pattern (regex)')
      .option('-c, --context <lines>', 'Context lines around matches', '5')
      .option('--case-sensitive', 'Case sensitive search')
      .action(async (filePath, options) => {
        await this.viewFile(filePath, options);
      });

    toolsCmd
      .command('edit <filePath>')
      .description('Edit file using string replacement')
      .option('-o, --old <string>', 'String to replace')
      .option('-n, --new <string>', 'Replacement string')
      .option('-l, --lines <start:end>', 'Line range for replacement')
      .action(async (filePath, options) => {
        await this.editFile(filePath, options);
      });

    toolsCmd
      .command('save <filePath>')
      .description('Save content to file')
      .option('-c, --content <content>', 'Content to save')
      .option('-f, --file <sourceFile>', 'Copy content from source file')
      .option('--overwrite', 'Overwrite existing file')
      .action(async (filePath, options) => {
        await this.saveFile(filePath, options);
      });

    toolsCmd
      .command('remove <filePaths...>')
      .description('Remove files with backup')
      .action(async (filePaths) => {
        await this.removeFiles(filePaths);
      });

    // Process management
    toolsCmd
      .command('run <command> [args...]')
      .description('Launch a process')
      .option('-w, --wait', 'Wait for process to complete')
      .option('-t, --timeout <seconds>', 'Timeout in seconds', '30')
      .option('-d, --cwd <directory>', 'Working directory')
      .option('-s, --stream', 'Stream output in real-time')
      .action(async (command, args, options) => {
        await this.runProcess(command, args, options);
      });

    toolsCmd
      .command('ps')
      .description('List running processes')
      .action(async () => {
        await this.listProcesses();
      });

    toolsCmd
      .command('kill <processId>')
      .description('Kill a process')
      .option('-s, --signal <signal>', 'Signal to send', 'SIGTERM')
      .action(async (processId, options) => {
        await this.killProcess(processId, options);
      });

    // Diagnostics
    toolsCmd
      .command('check <filePaths...>')
      .description('Check files for issues')
      .action(async (filePaths) => {
        await this.checkFiles(filePaths);
      });

    // Terminal
    toolsCmd
      .command('terminal')
      .description('Terminal management')
      .option('-c, --create', 'Create new terminal')
      .option('-l, --list', 'List terminals')
      .option('-r, --read <terminalId>', 'Read terminal output')
      .option('-w, --write <terminalId>', 'Write to terminal')
      .option('-k, --kill <terminalId>', 'Close terminal')
      .action(async (options) => {
        await this.manageTerminal(options);
      });

    // Package management
    toolsCmd
      .command('package')
      .description('Package management')
      .option('-d, --detect', 'Detect package managers')
      .option('-i, --install <packages>', 'Install packages')
      .option('-u, --uninstall <packages>', 'Uninstall packages')
      .option('-l, --list', 'List installed packages')
      .option('--audit', 'Security audit')
      .option('--manager <manager>', 'Specify package manager')
      .action(async (options) => {
        await this.managePackages(options);
      });

    // Workspace
    toolsCmd
      .command('workspace')
      .description('Workspace analysis')
      .option('-d, --detect', 'Detect project type')
      .option('-c, --config', 'Show project configuration')
      .option('-p, --path <path>', 'Project path', process.cwd())
      .action(async (options) => {
        await this.analyzeWorkspace(options);
      });

    // List available tools
    toolsCmd
      .command('list')
      .description('List available tools')
      .action(async () => {
        await this.listTools();
      });
  }

  private async viewFile(filePath: string, options: any): Promise<void> {
    try {
      const viewOptions: any = {};

      if (options.range) {
        const [start, end] = options.range.split(':').map(Number);
        viewOptions.range = { start, end: end || -1 };
      }

      if (options.search) {
        viewOptions.searchRegex = options.search;
        viewOptions.contextLines = parseInt(options.context);
        viewOptions.caseSensitive = options.caseSensitive;
      }

      const result = await this.registry.executeTool('file', 'view', filePath, viewOptions);
      
      if (result.success) {
        console.log(chalk.blue(`📄 ${filePath}`));
        console.log(chalk.dim('─'.repeat(60)));
        console.log(result.data.content);
        console.log(chalk.dim('─'.repeat(60)));
        console.log(chalk.dim(`Lines: ${result.data.lines}/${result.data.totalLines}`));
      } else {
        console.error(chalk.red('❌ Error:'), result.error);
      }
    } catch (error) {
      console.error(chalk.red('❌ Command failed:'), error);
    }
  }

  private async editFile(filePath: string, options: any): Promise<void> {
    try {
      if (!options.old || !options.new) {
        const answers = await inquirer.prompt([
          {
            type: 'input',
            name: 'oldStr',
            message: 'String to replace:',
            when: !options.old
          },
          {
            type: 'input',
            name: 'newStr',
            message: 'Replacement string:',
            when: !options.new
          }
        ]);

        options.old = options.old || answers.oldStr;
        options.new = options.new || answers.newStr;
      }

      const operation: any = {
        oldStr: options.old,
        newStr: options.new
      };

      if (options.lines) {
        const [start, end] = options.lines.split(':').map(Number);
        operation.startLine = start;
        operation.endLine = end;
      }

      const result = await this.registry.executeTool('file', 'strReplace', filePath, [operation]);
      
      if (result.success) {
        console.log(chalk.green('✅ File edited successfully'));
        console.log(chalk.dim(`Lines added: ${result.data.linesAdded}`));
        console.log(chalk.dim(`Lines deleted: ${result.data.linesDeleted}`));
        console.log(chalk.dim(`Backup: ${result.data.backup}`));
      } else {
        console.error(chalk.red('❌ Error:'), result.error);
      }
    } catch (error) {
      console.error(chalk.red('❌ Command failed:'), error);
    }
  }

  private async saveFile(filePath: string, options: any): Promise<void> {
    try {
      let content = options.content;

      if (options.file) {
        const fs = require('fs');
        content = fs.readFileSync(options.file, 'utf8');
      }

      if (!content) {
        const { inputContent } = await inquirer.prompt([
          {
            type: 'editor',
            name: 'inputContent',
            message: 'Enter file content:'
          }
        ]);
        content = inputContent;
      }

      const result = await this.registry.executeTool('file', 'saveFile', filePath, content, {
        overwrite: options.overwrite
      });
      
      if (result.success) {
        console.log(chalk.green('✅ File saved successfully'));
        console.log(chalk.dim(`Path: ${result.data.path}`));
        console.log(chalk.dim(`Lines: ${result.data.lines}`));
        if (result.data.backup) {
          console.log(chalk.dim(`Backup: ${result.data.backup}`));
        }
      } else {
        console.error(chalk.red('❌ Error:'), result.error);
      }
    } catch (error) {
      console.error(chalk.red('❌ Command failed:'), error);
    }
  }

  private async removeFiles(filePaths: string[]): Promise<void> {
    try {
      const { confirm } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'confirm',
          message: `Remove ${filePaths.length} file(s)?`,
          default: false
        }
      ]);

      if (!confirm) {
        console.log(chalk.yellow('Operation cancelled'));
        return;
      }

      const result = await this.registry.executeTool('file', 'removeFiles', filePaths);
      
      if (result.success) {
        console.log(chalk.green(`✅ Removed ${result.data.deletedCount} file(s)`));
        result.data.results.forEach((r: any) => {
          if (r.status === 'deleted') {
            console.log(chalk.dim(`  ✓ ${r.path} (backup: ${r.backup})`));
          } else {
            console.log(chalk.yellow(`  ⚠ ${r.path} (${r.status})`));
          }
        });
      } else {
        console.error(chalk.red('❌ Error:'), result.error);
      }
    } catch (error) {
      console.error(chalk.red('❌ Command failed:'), error);
    }
  }

  private async runProcess(command: string, args: string[], options: any): Promise<void> {
    try {
      const launchOptions: any = {
        wait: options.wait,
        maxWaitSeconds: parseInt(options.timeout),
        streamOutput: options.stream
      };

      if (options.cwd) {
        launchOptions.cwd = options.cwd;
      }

      console.log(chalk.blue(`🚀 Running: ${command} ${args.join(' ')}`));
      
      const result = await this.registry.executeTool('process', 'launchProcess', command, args, launchOptions);
      
      if (result.success) {
        if (options.wait) {
          console.log(chalk.green(`✅ Process completed (exit code: ${result.data.exitCode})`));
          if (result.data.stdout) {
            console.log(chalk.dim('Output:'));
            console.log(result.data.stdout);
          }
          if (result.data.stderr) {
            console.log(chalk.red('Errors:'));
            console.log(result.data.stderr);
          }
        } else {
          console.log(chalk.green(`✅ Process started (ID: ${result.data.processId})`));
          console.log(chalk.dim(`PID: ${result.data.pid}`));
        }
      } else {
        console.error(chalk.red('❌ Error:'), result.error);
      }
    } catch (error) {
      console.error(chalk.red('❌ Command failed:'), error);
    }
  }

  private async listProcesses(): Promise<void> {
    try {
      const result = await this.registry.executeTool('process', 'listProcesses');
      
      if (result.success) {
        console.log(chalk.blue('📋 Running Processes'));
        console.log(chalk.dim('─'.repeat(80)));
        
        if (result.data.processes.length === 0) {
          console.log(chalk.yellow('No processes found'));
          return;
        }

        console.log(chalk.bold('ID'.padEnd(20)) + chalk.bold('Command'.padEnd(30)) + chalk.bold('Status'.padEnd(15)) + chalk.bold('PID'));
        console.log(chalk.dim('─'.repeat(80)));

        result.data.processes.forEach((proc: any) => {
          const statusColor = proc.status === 'running' ? chalk.green : 
                             proc.status === 'completed' ? chalk.blue : chalk.red;
          
          console.log(
            proc.id.substring(0, 18).padEnd(20) +
            `${proc.command} ${proc.args.join(' ')}`.substring(0, 28).padEnd(30) +
            statusColor(proc.status.padEnd(15)) +
            chalk.dim(proc.pid || 'N/A')
          );
        });

        console.log(chalk.dim('─'.repeat(80)));
        console.log(chalk.dim(`Total: ${result.data.total}, Running: ${result.data.running}`));
      } else {
        console.error(chalk.red('❌ Error:'), result.error);
      }
    } catch (error) {
      console.error(chalk.red('❌ Command failed:'), error);
    }
  }

  private async killProcess(processId: string, options: any): Promise<void> {
    try {
      const result = await this.registry.executeTool('process', 'killProcess', processId, options.signal);
      
      if (result.success) {
        console.log(chalk.green(`✅ Process ${processId} terminated`));
        console.log(chalk.dim(`Signal: ${result.data.signal}`));
      } else {
        console.error(chalk.red('❌ Error:'), result.error);
      }
    } catch (error) {
      console.error(chalk.red('❌ Command failed:'), error);
    }
  }

  private async checkFiles(filePaths: string[]): Promise<void> {
    try {
      console.log(chalk.blue('🔍 Analyzing files...'));
      
      const result = await this.registry.executeTool('diagnostics', 'getDiagnostics', filePaths);
      
      if (result.success) {
        const summary = result.data;
        
        console.log(chalk.blue('📊 Diagnostics Summary'));
        console.log(chalk.dim('─'.repeat(40)));
        console.log(`Files analyzed: ${summary.totalFiles}`);
        console.log(`Total issues: ${summary.totalIssues}`);
        console.log(`Errors: ${chalk.red(summary.errors)}`);
        console.log(`Warnings: ${chalk.yellow(summary.warnings)}`);
        console.log(`Info: ${chalk.blue(summary.infos)}`);
        console.log(`Hints: ${chalk.dim(summary.hints)}`);

        if (summary.totalIssues > 0) {
          console.log(chalk.blue('\n📋 Issues by File'));
          console.log(chalk.dim('─'.repeat(60)));

          for (const [file, diagnostics] of Object.entries(summary.fileIssues)) {
            if ((diagnostics as any[]).length > 0) {
              console.log(chalk.bold(`\n${file}:`));
              (diagnostics as any[]).forEach(diag => {
                const severityColor = diag.severity === 'error' ? chalk.red :
                                    diag.severity === 'warning' ? chalk.yellow :
                                    diag.severity === 'info' ? chalk.blue : chalk.dim;
                
                console.log(`  ${severityColor(diag.severity.toUpperCase())} Line ${diag.line}:${diag.column} - ${diag.message}`);
                if (diag.code) {
                  console.log(chalk.dim(`    Code: ${diag.code} (${diag.source})`));
                }
              });
            }
          }
        }
      } else {
        console.error(chalk.red('❌ Error:'), result.error);
      }
    } catch (error) {
      console.error(chalk.red('❌ Command failed:'), error);
    }
  }

  private async manageTerminal(options: any): Promise<void> {
    try {
      if (options.create) {
        const result = await this.registry.executeTool('terminal', 'createTerminal');
        if (result.success) {
          console.log(chalk.green(`✅ Terminal created: ${result.data.terminalId}`));
          console.log(chalk.dim(`Shell: ${result.data.shell}`));
          console.log(chalk.dim(`PID: ${result.data.pid}`));
        } else {
          console.error(chalk.red('❌ Error:'), result.error);
        }
      } else if (options.list) {
        const result = await this.registry.executeTool('terminal', 'listTerminals');
        if (result.success) {
          console.log(chalk.blue('📋 Terminals'));
          console.log(chalk.dim('─'.repeat(60)));
          
          if (result.data.terminals.length === 0) {
            console.log(chalk.yellow('No terminals found'));
            return;
          }

          result.data.terminals.forEach((term: any) => {
            const statusColor = term.isActive ? chalk.green : chalk.red;
            console.log(`${term.id.substring(0, 12)} - ${statusColor(term.isActive ? 'Active' : 'Inactive')} - ${term.shell}`);
            console.log(chalk.dim(`  Created: ${new Date(term.createdAt).toLocaleString()}`));
          });

          console.log(chalk.dim(`\nTotal: ${result.data.total}, Active: ${result.data.active}`));
        } else {
          console.error(chalk.red('❌ Error:'), result.error);
        }
      } else if (options.read) {
        const result = await this.registry.executeTool('terminal', 'readTerminal', options.read);
        if (result.success) {
          console.log(chalk.blue(`📖 Terminal Output (${result.data.terminalId}):`));
          console.log(chalk.dim('─'.repeat(60)));
          console.log(result.data.output);
        } else {
          console.error(chalk.red('❌ Error:'), result.error);
        }
      } else if (options.write) {
        const { command } = await inquirer.prompt([
          {
            type: 'input',
            name: 'command',
            message: 'Command to execute:'
          }
        ]);

        const result = await this.registry.executeTool('terminal', 'writeTerminal', options.write, command);
        if (result.success) {
          console.log(chalk.green(`✅ Command sent to terminal ${result.data.terminalId}`));
        } else {
          console.error(chalk.red('❌ Error:'), result.error);
        }
      } else if (options.kill) {
        const result = await this.registry.executeTool('terminal', 'closeTerminal', options.kill);
        if (result.success) {
          console.log(chalk.green(`✅ Terminal ${result.data.terminalId} closed`));
        } else {
          console.error(chalk.red('❌ Error:'), result.error);
        }
      } else {
        console.log(chalk.yellow('Please specify an action: --create, --list, --read, --write, or --kill'));
      }
    } catch (error) {
      console.error(chalk.red('❌ Command failed:'), error);
    }
  }

  private async managePackages(options: any): Promise<void> {
    try {
      if (options.detect) {
        const result = await this.registry.executeTool('package', 'detectPackageManagers');
        if (result.success) {
          console.log(chalk.blue('📦 Package Managers'));
          console.log(chalk.dim('─'.repeat(40)));
          console.log(`Detected: ${result.data.detected.join(', ') || 'None'}`);
          console.log(`Available: ${result.data.available.map((a: any) => `${a.name} (${a.version})`).join(', ') || 'None'}`);
          if (result.data.recommended) {
            console.log(`Recommended: ${chalk.green(result.data.recommended)}`);
          }
        } else {
          console.error(chalk.red('❌ Error:'), result.error);
        }
      } else if (options.install) {
        const packages = options.install.split(',').map((p: string) => p.trim());
        const result = await this.registry.executeTool('package', 'installPackages', packages, {
          manager: options.manager
        });
        if (result.success) {
          console.log(chalk.green(`✅ Packages installed using ${result.data.manager}`));
          console.log(chalk.dim(`Packages: ${result.data.packages.join(', ')}`));
        } else {
          console.error(chalk.red('❌ Error:'), result.error);
        }
      } else if (options.list) {
        const result = await this.registry.executeTool('package', 'listPackages', {
          manager: options.manager
        });
        if (result.success) {
          console.log(chalk.blue(`📋 Installed Packages (${result.data.manager})`));
          console.log(chalk.dim('─'.repeat(60)));
          
          if (result.data.packages.length === 0) {
            console.log(chalk.yellow('No packages found'));
            return;
          }

          result.data.packages.forEach((pkg: any) => {
            console.log(`${pkg.name}@${pkg.version} (${pkg.type})`);
          });
        } else {
          console.error(chalk.red('❌ Error:'), result.error);
        }
      } else if (options.audit) {
        const result = await this.registry.executeTool('package', 'auditPackages', {
          manager: options.manager
        });
        if (result.success) {
          console.log(chalk.blue(`🔒 Security Audit (${result.data.manager})`));
          console.log(chalk.dim('─'.repeat(40)));
          
          if (result.data.hasVulnerabilities) {
            console.log(chalk.red('⚠️ Vulnerabilities found'));
            console.log(result.data.output);
          } else {
            console.log(chalk.green('✅ No vulnerabilities found'));
          }
        } else {
          console.error(chalk.red('❌ Error:'), result.error);
        }
      } else {
        console.log(chalk.yellow('Please specify an action: --detect, --install, --list, or --audit'));
      }
    } catch (error) {
      console.error(chalk.red('❌ Command failed:'), error);
    }
  }

  private async analyzeWorkspace(options: any): Promise<void> {
    try {
      if (options.detect) {
        const result = await this.registry.executeTool('workspace', 'detectWorkspace', options.path);
        if (result.success) {
          const workspace = result.data;
          
          console.log(chalk.blue('🏗️ Workspace Analysis'));
          console.log(chalk.dim('─'.repeat(50)));
          console.log(`Path: ${workspace.rootPath}`);
          console.log(`Primary Type: ${chalk.green(workspace.primaryType.name)} (${workspace.primaryType.language})`);
          if (workspace.primaryType.framework) {
            console.log(`Framework: ${workspace.primaryType.framework}`);
          }
          console.log(`Confidence: ${workspace.primaryType.confidence}%`);
          
          if (workspace.projectTypes.length > 1) {
            console.log(chalk.blue('\n📊 All Detected Types:'));
            workspace.projectTypes.forEach((type: any) => {
              console.log(`  ${type.name} (${type.confidence}%) - ${type.language}`);
            });
          }

          console.log(chalk.blue('\n📁 Structure:'));
          console.log(`Files: ${workspace.totalFiles}`);
          console.log(`Directories: ${workspace.totalDirectories}`);
          
          if (workspace.packageManagers.length > 0) {
            console.log(`Package Managers: ${workspace.packageManagers.join(', ')}`);
          }

          if (workspace.gitRepository?.isRepository) {
            console.log(chalk.blue('\n🔗 Git Repository:'));
            if (workspace.gitRepository.branch) {
              console.log(`Branch: ${workspace.gitRepository.branch}`);
            }
            if (workspace.gitRepository.remotes) {
              console.log(`Remotes: ${workspace.gitRepository.remotes.join(', ')}`);
            }
          }
        } else {
          console.error(chalk.red('❌ Error:'), result.error);
        }
      } else if (options.config) {
        const result = await this.registry.executeTool('workspace', 'getProjectConfig', options.path);
        if (result.success) {
          console.log(chalk.blue('⚙️ Project Configuration'));
          console.log(chalk.dim('─'.repeat(50)));
          
          for (const [file, config] of Object.entries(result.data.configs)) {
            console.log(chalk.bold(`\n${file}:`));
            if (typeof config === 'object' && config && !(config as any).error) {
              console.log(JSON.stringify(config, null, 2));
            } else if (config && (config as any).error) {
              console.log(chalk.red(`Error: ${(config as any).error}`));
            } else {
              console.log(config);
            }
          }
        } else {
          console.error(chalk.red('❌ Error:'), result.error);
        }
      } else {
        console.log(chalk.yellow('Please specify an action: --detect or --config'));
      }
    } catch (error) {
      console.error(chalk.red('❌ Command failed:'), error);
    }
  }

  private async listTools(): Promise<void> {
    try {
      const tools = this.registry.getAvailableTools();
      
      console.log(chalk.blue('🛠️ Available Tools'));
      console.log(chalk.dim('─'.repeat(40)));
      
      const toolDescriptions = {
        file: 'File operations (view, edit, save, remove)',
        process: 'Process management (launch, monitor, kill)',
        diagnostics: 'Code analysis and error detection',
        terminal: 'Terminal session management',
        package: 'Package manager operations',
        workspace: 'Project detection and analysis'
      };

      tools.forEach(tool => {
        console.log(`${chalk.green(tool.padEnd(12))} - ${toolDescriptions[tool as keyof typeof toolDescriptions] || 'Tool description'}`);
      });

      console.log(chalk.dim(`\nTotal: ${tools.length} tools available`));
      console.log(chalk.dim('Use "goc tools <tool-command> --help" for specific tool usage'));
    } catch (error) {
      console.error(chalk.red('❌ Command failed:'), error);
    }
  }
}
