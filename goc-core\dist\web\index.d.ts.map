{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../src/web/index.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,UAAU,CAAC;AAGpD,MAAM,WAAW,oBAAqB,SAAQ,YAAY;IACxD,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,cAAc,EAAE,MAAM,CAAC;IACvB,MAAM,EAAE,MAAM,CAAC;IACf,aAAa,CAAC,EAAE,IAAI,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAChC;AAED,MAAM,WAAW,gBAAgB;IAC/B,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,MAAM,CAAC,EAAE,MAAM,CAAC;IAChB,SAAS,CAAC,EAAE,KAAK,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC;IACtD,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,cAAc,CAAC,EAAE,OAAO,CAAC;IACzB,aAAa,CAAC,EAAE,MAAM,EAAE,CAAC;IACzB,cAAc,CAAC,EAAE,MAAM,EAAE,CAAC;CAC3B;AAED,MAAM,WAAW,uBAAuB;IACtC,KAAK,EAAE,MAAM,CAAC;IACd,OAAO,EAAE,MAAM,CAAC;IAChB,OAAO,EAAE,MAAM,CAAC;IAChB,YAAY,EAAE,KAAK,CAAC;QAClB,QAAQ,EAAE,MAAM,CAAC;QACjB,IAAI,EAAE,MAAM,CAAC;QACb,WAAW,CAAC,EAAE,MAAM,CAAC;KACtB,CAAC,CAAC;IACH,KAAK,EAAE,KAAK,CAAC;QACX,GAAG,EAAE,MAAM,CAAC;QACZ,KAAK,EAAE,MAAM,CAAC;QACd,IAAI,EAAE,UAAU,GAAG,UAAU,CAAC;KAC/B,CAAC,CAAC;IACH,QAAQ,EAAE;QACR,SAAS,EAAE,MAAM,CAAC;QAClB,WAAW,EAAE,MAAM,CAAC;QACpB,QAAQ,EAAE,MAAM,CAAC;QACjB,aAAa,CAAC,EAAE,IAAI,CAAC;QACrB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;KACjB,CAAC;CACH;AAED,MAAM,WAAW,aAAa;IAC5B,IAAI,EAAE,QAAQ,GAAG,oBAAoB,GAAG,gBAAgB,GAAG,kBAAkB,CAAC;IAC9E,KAAK,CAAC,EAAE,MAAM,CAAC;IACf,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,QAAQ,CAAC,EAAE,MAAM,EAAE,CAAC;IACpB,SAAS,EAAE,IAAI,CAAC;IAChB,QAAQ,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAChC;AAED,MAAM,WAAW,aAAa;IAC5B,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,QAAQ,GAAG,QAAQ,GAAG,WAAW,CAAC;IACzC,UAAU,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,MAAM,EAAE,CAAC;IACxB,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,MAAM,EAAE,CAAC;CACnB;AAED;;GAEG;AACH,qBAAa,aAAa;IACxB,OAAO,CAAC,aAAa,CAA+B;IACpD,OAAO,CAAC,cAAc,CAAuB;;IAM7C;;OAEG;IACG,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,GAAE,gBAAqB,GAAG,OAAO,CAAC,oBAAoB,EAAE,CAAC;IAkC5F;;OAEG;IACG,YAAY,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC;IA8CpD;;OAEG;IACG,cAAc,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,GAAE;QACzC,cAAc,CAAC,EAAE,OAAO,CAAC;QACzB,WAAW,CAAC,EAAE,OAAO,CAAC;QACtB,eAAe,CAAC,EAAE,OAAO,CAAC;KACtB,GAAG,OAAO,CAAC,uBAAuB,CAAC;IA4CzC;;OAEG;IACG,gBAAgB,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;IA2BxD;;OAEG;IACG,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,OAAO,GAAE;QAC1C,KAAK,CAAC,EAAE,OAAO,GAAG,cAAc,GAAG,UAAU,CAAC;QAC9C,eAAe,CAAC,EAAE,OAAO,CAAC;QAC1B,aAAa,CAAC,EAAE,OAAO,CAAC;QACxB,UAAU,CAAC,EAAE,MAAM,CAAC;KAChB,GAAG,OAAO,CAAC;QACf,OAAO,EAAE,MAAM,CAAC;QAChB,SAAS,EAAE,MAAM,EAAE,CAAC;QACpB,YAAY,EAAE,KAAK,CAAC;YAClB,QAAQ,EAAE,MAAM,CAAC;YACjB,IAAI,EAAE,MAAM,CAAC;YACb,WAAW,EAAE,MAAM,CAAC;YACpB,MAAM,EAAE,MAAM,CAAC;SAChB,CAAC,CAAC;QACH,MAAM,CAAC,EAAE,aAAa,EAAE,CAAC;QACzB,OAAO,EAAE,oBAAoB,EAAE,CAAC;QAChC,aAAa,EAAE,MAAM,EAAE,CAAC;KACzB,CAAC;IA8FF;;OAEG;IACG,mBAAmB,IAAI,OAAO,CAAC;QACnC,WAAW,EAAE,KAAK,CAAC;YAAE,KAAK,EAAE,MAAM,CAAC;YAAC,KAAK,EAAE,MAAM,CAAA;SAAE,CAAC,CAAC;QACrD,kBAAkB,EAAE,KAAK,CAAC;YAAE,OAAO,EAAE,MAAM,CAAC;YAAC,SAAS,EAAE,MAAM,CAAC;YAAC,OAAO,EAAE,MAAM,EAAE,CAAA;SAAE,CAAC,CAAC;QACrF,iBAAiB,EAAE,MAAM,EAAE,CAAC;QAC5B,oBAAoB,EAAE,MAAM,EAAE,CAAC;KAChC,CAAC;IA0DF,OAAO,CAAC,uBAAuB;YAQjB,gBAAgB;YA4DhB,cAAc;YA6Bd,kBAAkB;YAgBlB,kBAAkB;IAiBhC,OAAO,CAAC,YAAY;IAKpB,OAAO,CAAC,kBAAkB;IAY1B,OAAO,CAAC,mBAAmB;IA8B3B,OAAO,CAAC,YAAY;IAoBpB,OAAO,CAAC,cAAc;IAStB,OAAO,CAAC,oBAAoB;IAsB5B,OAAO,CAAC,aAAa;IAkBrB,OAAO,CAAC,WAAW;IA4BnB,OAAO,CAAC,sBAAsB;YAchB,mBAAmB;YAWnB,qBAAqB;YAyBrB,qBAAqB;IAenC,OAAO,CAAC,uBAAuB;IA4B/B,OAAO,CAAC,kBAAkB;YAYZ,oBAAoB;YAMpB,gBAAgB;YAUhB,aAAa;YAcb,oBAAoB;IAMlC,OAAO,CAAC,uBAAuB;IAO/B,OAAO,CAAC,2BAA2B;CAQpC;AAED,cAAc,UAAU,CAAC"}