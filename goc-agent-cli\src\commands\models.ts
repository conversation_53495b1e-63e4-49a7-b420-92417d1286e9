/**
 * Model Selection Commands
 * 
 * Interactive commands for selecting between Ollama and GOC Agent models
 */

import { Command } from 'commander';
import chalk from 'chalk';
import inquirer from 'inquirer';
import { ConfigManager } from '../../services/ConfigManager';
import { BackendAPIClient } from '../../services/BackendAPIClient';

export class ModelsCommand {
  private configManager: ConfigManager;
  private apiClient: BackendAPIClient;

  constructor() {
    this.configManager = new ConfigManager();
    this.apiClient = new BackendAPIClient();
  }

  register(program: Command): void {
    const modelsCmd = program
      .command('models')
      .description('Manage AI model selection');

    modelsCmd
      .command('select')
      .description('Select AI provider and model')
      .action(async () => {
        await this.selectModel();
      });

    modelsCmd
      .command('list')
      .description('List available models')
      .action(async () => {
        await this.listModels();
      });

    modelsCmd
      .command('status')
      .description('Show current model selection')
      .action(async () => {
        await this.showStatus();
      });

    modelsCmd
      .command('test')
      .description('Test current model connection')
      .action(async () => {
        await this.testModel();
      });
  }

  private async selectModel(): Promise<void> {
    console.log(chalk.blue('🤖 GOC Agent Model Selection\n'));

    try {
      // Step 1: Select Provider
      const provider = await this.selectProvider();
      if (!provider) return;

      // Step 2: Select Model based on provider
      if (provider === 'ollama') {
        await this.selectOllamaModel();
      } else if (provider === 'goc') {
        await this.selectGocAgentModel();
      }

      console.log(chalk.green('\n✅ Model selection completed!'));
      await this.showStatus();

    } catch (error) {
      console.error(chalk.red('❌ Model selection failed:'), error);
    }
  }

  private async selectProvider(): Promise<string | null> {
    const { provider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'provider',
        message: 'Choose AI provider:',
        choices: [
          {
            name: 'Ollama (Local) - Free local AI models',
            value: 'ollama',
            short: 'Ollama'
          },
          {
            name: 'GOC Agent Model - Hosted AI models (registration required)',
            value: 'goc',
            short: 'GOC Agent'
          }
        ]
      }
    ]);

    this.configManager.setDefaultProvider(provider);
    
    if (provider === 'ollama') {
      console.log(chalk.dim('Selected: Ollama (Local models)'));
    } else {
      console.log(chalk.dim('Selected: GOC Agent Model (Hosted models)'));
    }

    return provider;
  }

  private async selectOllamaModel(): Promise<void> {
    console.log(chalk.yellow('\n🔍 Checking Ollama installation...'));

    const isAvailable = await this.checkOllamaAvailability();
    if (!isAvailable) {
      await this.showOllamaSetup();
      return;
    }

    const models = await this.getOllamaModels();
    if (models.length === 0) {
      await this.showOllamaModelSetup();
      return;
    }

    const { model } = await inquirer.prompt([
      {
        type: 'list',
        name: 'model',
        message: 'Select Ollama model:',
        choices: models.map(model => ({
          name: `${this.formatOllamaModelName(model)} - ${this.getOllamaModelDescription(model)}`,
          value: model,
          short: this.formatOllamaModelName(model)
        }))
      }
    ]);

    this.configManager.setDefaultModel(model);
    console.log(chalk.green(`✅ Selected: ${this.formatOllamaModelName(model)}`));
  }

  private async selectGocAgentModel(): Promise<void> {
    console.log(chalk.yellow('\n☁️  GOC Agent Model Selection'));

    const { model } = await inquirer.prompt([
      {
        type: 'list',
        name: 'model',
        message: 'Select GOC Agent model tier:',
        choices: [
          {
            name: 'Free Tier - 50 requests/month forever (Perfect for trying GOC Agent)',
            value: 'goc-agent-cloud',
            short: 'Free Tier'
          },
          {
            name: 'Developer Tier - $29/month for 500 requests (Full-featured)',
            value: 'goc-agent-dev',
            short: 'Developer Tier'
          }
        ]
      }
    ]);

    this.configManager.setDefaultModel(model);
    
    const tierName = model === 'goc-agent-cloud' ? 'Free Tier' : 'Developer Tier';
    console.log(chalk.green(`✅ Selected: GOC Agent Model (${tierName})`));

    // Check authentication
    if (!this.apiClient.isAuthenticated()) {
      await this.handleGocAgentAuth();
    }
  }

  private async handleGocAgentAuth(): Promise<void> {
    console.log(chalk.yellow('\n🔐 Authentication Required'));
    console.log('GOC Agent models require registration and authentication.');

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to do?',
        choices: [
          { name: 'Register new account', value: 'register' },
          { name: 'Login with existing account', value: 'login' },
          { name: 'Skip for now', value: 'skip' }
        ]
      }
    ]);

    if (action === 'register') {
      console.log(chalk.blue('\n📝 Opening registration page...'));
      console.log('Visit: https://goc-agent.com/register');
      console.log('After registration, use "goc auth login" to authenticate.');
    } else if (action === 'login') {
      console.log(chalk.blue('\n🔑 Use "goc auth login" to authenticate.'));
    } else {
      console.log(chalk.dim('\n⚠️  You can authenticate later using "goc auth login"'));
    }
  }

  private async checkOllamaAvailability(): Promise<boolean> {
    try {
      const response = await fetch('http://localhost:11434/api/tags', {
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  private async getOllamaModels(): Promise<string[]> {
    try {
      const response = await fetch('http://localhost:11434/api/tags');
      if (!response.ok) return [];

      const data = await response.json();
      return ((data as any).models || []).map((model: any) => model.name);
    } catch (error) {
      return [];
    }
  }

  private async showOllamaSetup(): Promise<void> {
    console.log(chalk.red('\n❌ Ollama not found'));
    console.log('To use local AI models, you need to install Ollama:');
    console.log(chalk.cyan('1. Visit: https://ollama.ai'));
    console.log(chalk.cyan('2. Download and install Ollama'));
    console.log(chalk.cyan('3. Pull a model: ollama pull llama3.2:3b'));
    console.log(chalk.cyan('4. Run: goc models select'));
  }

  private async showOllamaModelSetup(): Promise<void> {
    console.log(chalk.yellow('\n⚠️  No Ollama models found'));
    console.log('Install recommended models:');
    console.log(chalk.cyan('ollama pull llama3.2:3b      # Recommended for most tasks'));
    console.log(chalk.cyan('ollama pull codellama:7b     # Specialized for coding'));
    console.log(chalk.cyan('ollama pull mistral:7b       # General purpose'));
  }

  private formatOllamaModelName(modelId: string): string {
    const nameMap: Record<string, string> = {
      'llama3.2:3b': 'Llama 3.2 3B',
      'llama3.2:1b': 'Llama 3.2 1B',
      'codellama:7b': 'Code Llama 7B',
      'codellama:13b': 'Code Llama 13B',
      'mistral:7b': 'Mistral 7B',
      'deepseek-coder:6.7b': 'DeepSeek Coder 6.7B',
      'qwen2.5-coder:7b': 'Qwen2.5 Coder 7B'
    };
    return nameMap[modelId] || modelId;
  }

  private getOllamaModelDescription(modelId: string): string {
    const descMap: Record<string, string> = {
      'llama3.2:3b': 'Recommended for most tasks',
      'llama3.2:1b': 'Lightweight and fast',
      'codellama:7b': 'Specialized for coding',
      'codellama:13b': 'Advanced coding model',
      'mistral:7b': 'General purpose',
      'deepseek-coder:6.7b': 'Optimized for code',
      'qwen2.5-coder:7b': 'Latest coding model'
    };
    return descMap[modelId] || 'Local AI model';
  }

  private async listModels(): Promise<void> {
    console.log(chalk.blue('📋 Available Models\n'));

    // Show Ollama models
    console.log(chalk.green('🏠 Ollama (Local Models)'));
    const ollamaAvailable = await this.checkOllamaAvailability();
    
    if (ollamaAvailable) {
      const models = await this.getOllamaModels();
      if (models.length > 0) {
        models.forEach(model => {
          console.log(`  • ${this.formatOllamaModelName(model)} - ${this.getOllamaModelDescription(model)}`);
        });
      } else {
        console.log(chalk.dim('  No models installed. Run "ollama pull <model>" to install.'));
      }
    } else {
      console.log(chalk.dim('  Ollama not installed. Visit https://ollama.ai'));
    }

    // Show GOC Agent models
    console.log(chalk.blue('\n☁️  GOC Agent Models'));
    console.log('  • Free Tier - 50 requests/month forever');
    console.log('  • Developer Tier - $29/month for 500 requests');
    console.log(chalk.dim('  Requires registration at https://goc-agent.com'));
  }

  private async showStatus(): Promise<void> {
    const config = await this.configManager.getConfig();
    const provider = config.defaultProvider;
    const model = config.defaultModel;

    console.log(chalk.blue('\n📊 Current Model Status'));
    console.log(`Provider: ${chalk.green(provider === 'ollama' ? 'Ollama (Local)' : 'GOC Agent Model')}`);

    if (model) {
      if (provider === 'ollama') {
        console.log(`Model: ${chalk.green(this.formatOllamaModelName(model))}`);

        // Check Ollama connection
        const isRunning = await this.checkOllamaAvailability();
        console.log(`Status: ${isRunning ? chalk.green('✅ Connected') : chalk.red('❌ Ollama not running')}`);

        if (isRunning) {
          const models = await this.getOllamaModels();
          const isInstalled = models.includes(model);
          console.log(`Model Status: ${isInstalled ? chalk.green('✅ Installed') : chalk.red('❌ Not installed')}`);
        }
      } else {
        const tierName = model === 'goc-agent-cloud' ? 'Free Tier' : 'Developer Tier';
        console.log(`Model: ${chalk.green(`GOC Agent (${tierName})`)}`);

        // Show authentication status for GOC Agent
        const isAuth = this.apiClient.isAuthenticated();
        console.log(`Authentication: ${isAuth ? chalk.green('✅ Authenticated') : chalk.red('❌ Not authenticated')}`);

        if (isAuth) {
          console.log(`Tier: ${chalk.blue(tierName)}`);
          if (model === 'goc-agent-cloud') {
            console.log(`Limits: ${chalk.dim('50 requests/month, 10 requests/day')}`);
          } else {
            console.log(`Limits: ${chalk.dim('500 requests/month, 50 requests/day')}`);
          }
        }
      }
    } else {
      console.log(`Model: ${chalk.red('Not selected')}`);
      console.log(chalk.yellow('💡 Run "goc models select" to choose a model'));
    }

    // Show quick setup tips
    if (provider === 'ollama') {
      const isRunning = await this.checkOllamaAvailability();
      if (!isRunning) {
        console.log(chalk.yellow('\n💡 Tips:'));
        console.log(chalk.dim('   • Install Ollama from https://ollama.ai'));
        console.log(chalk.dim('   • Start Ollama service'));
        console.log(chalk.dim('   • Pull models: ollama pull llama3.2:3b'));
      }
    } else if (provider === 'goc' && !this.apiClient.isAuthenticated()) {
      console.log(chalk.yellow('\n💡 Tips:'));
      console.log(chalk.dim('   • Register at https://goc-agent.com/register'));
      console.log(chalk.dim('   • Use "goc auth login" to authenticate'));
    }
  }

  private async testModel(): Promise<void> {
    console.log(chalk.blue('🧪 Testing model connection...\n'));
    
    const config = await this.configManager.getConfig();
    const provider = config.defaultProvider;

    if (provider === 'ollama') {
      const available = await this.checkOllamaAvailability();
      if (available) {
        console.log(chalk.green('✅ Ollama is running and accessible'));
      } else {
        console.log(chalk.red('❌ Ollama is not running or not accessible'));
      }
    } else if (provider === 'goc') {
      if (this.apiClient.isAuthenticated()) {
        console.log(chalk.green('✅ GOC Agent authentication valid'));
      } else {
        console.log(chalk.red('❌ GOC Agent authentication required'));
      }
    }
  }
}
