module.exports = {
    root: true,
    parser: '@typescript-eslint/parser',
    parserOptions: {
        ecmaVersion: 6,
        sourceType: 'module',
    },
    plugins: [
        '@typescript-eslint',
    ],
    extends: [
        'eslint:recommended',
    ],
    env: {
        node: true,
        es6: true,
        browser: true
    },
    globals: {
        console: 'readonly',
        Buffer: 'readonly',
        setTimeout: 'readonly',
        clearTimeout: 'readonly',
        setInterval: 'readonly',
        clearInterval: 'readonly',
        require: 'readonly',
        process: 'readonly',
        NodeJS: 'readonly',
        AbortSignal: 'readonly'
    },
    rules: {
        'curly': 'off',
        'eqeqeq': 'warn',
        'no-throw-literal': 'warn',
        'semi': 'warn',
        'no-unused-vars': ['warn', {
            'argsIgnorePattern': '^_',
            'varsIgnorePattern': '^_',
            'args': 'after-used'
        }],
        'no-empty-function': 'off',
        'no-undef': 'warn'
    },
    ignorePatterns: [
        'out',
        'dist',
        '**/*.d.ts',
        'node_modules'
    ]
};
