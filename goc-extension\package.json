{"name": "goc-agent", "displayName": "GOC Agent", "description": "AI-powered coding assistant with multi-provider support and intelligent context awareness", "version": "1.0.0", "publisher": "goc-agent", "engines": {"vscode": "^1.74.0"}, "author": "GOC Agent Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/goc-agent/goc-agent.git"}, "categories": ["Other", "Machine Learning", "Programming Languages", "Snippets", "Debuggers"], "keywords": ["ai", "assistant", "coding", "goc-agent", "openai", "groq", "gemini", "claude", "context", "intelligent"], "activationEvents": ["onStartupFinished", "onCommand:gocAgent.chat", "onCommand:gocAgent.explainCode", "onCommand:gocAgent.generateCode", "onLanguage:typescript", "onLanguage:javascript", "onLanguage:python", "onLanguage:php"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "gocAgent.openChat", "title": "Open Chat", "category": "GOC Agent", "icon": "$(comment-discussion)"}, {"command": "gocAgent.explainCode", "title": "Explain Selected Code", "category": "GOC Agent", "icon": "$(question)"}, {"command": "gocAgent.generateCode", "title": "Generate Code", "category": "GOC Agent", "icon": "$(add)"}, {"command": "gocAgent.fixIssues", "title": "Fix Issues", "category": "GOC Agent", "icon": "$(tools)"}, {"command": "gocAgent.refactorCode", "title": "Refactor Code", "category": "GOC Agent", "icon": "$(symbol-class)"}, {"command": "gocAgent.generateTests", "title": "Generate Tests", "category": "GOC Agent", "icon": "$(beaker)"}, {"command": "gocAgent.analyzeProject", "title": "Analyze Project", "category": "GOC Agent", "icon": "$(search)"}, {"command": "gocAgent.webResearch", "title": "Web Research", "category": "GOC Agent", "icon": "$(globe)"}, {"command": "gocAgent.trainTechnology", "title": "Train Technology", "category": "GOC Agent", "icon": "$(mortar-board)"}, {"command": "gocAgent.selectProvider", "title": "Select AI Provider", "category": "GOC Agent", "icon": "$(settings-gear)"}, {"command": "gocAgent.selectModel", "title": "Select AI Model", "category": "GOC Agent", "icon": "$(chip)"}, {"command": "gocAgent.openSettings", "title": "Open Settings", "category": "GOC Agent", "icon": "$(gear)"}, {"command": "gocAgent.clearChat", "title": "Clear Chat", "category": "GOC Agent", "icon": "$(clear-all)"}, {"command": "gocAgent.showStatus", "title": "Show Status", "category": "GOC Agent", "icon": "$(info)"}], "menus": {"editor/context": [{"command": "gocAgent.explainCode", "when": "editorHasSelection", "group": "goc-agent@1"}, {"command": "gocAgent.refactorCode", "when": "editorHasSelection", "group": "goc-agent@2"}, {"command": "gocAgent.generateTests", "when": "editorTextFocus", "group": "goc-agent@3"}, {"command": "gocAgent.fixIssues", "when": "editorTextFocus", "group": "goc-agent@4"}], "explorer/context": [{"command": "gocAgent.analyzeProject", "when": "explorerResourceIsFolder", "group": "goc-agent@1"}], "commandPalette": [{"command": "gocAgent.explainCode", "when": "editorHasSelection"}]}, "keybindings": [{"command": "gocAgent.openChat", "key": "ctrl+alt+g", "mac": "cmd+alt+g"}, {"command": "gocAgent.explainCode", "key": "ctrl+alt+e", "mac": "cmd+alt+e", "when": "editorHasSelection"}, {"command": "gocAgent.fixIssues", "key": "ctrl+alt+f", "mac": "cmd+alt+f"}, {"command": "gocAgent.generateTests", "key": "ctrl+alt+t", "mac": "cmd+alt+t"}, {"command": "gocAgent.refactorCode", "key": "ctrl+alt+r", "mac": "cmd+alt+r", "when": "editorHasSelection"}], "configuration": {"title": "GOC Agent", "properties": {"gocAgent.provider": {"type": "string", "default": "groq", "enum": ["openai", "groq", "gemini", "claude"], "enumDescriptions": ["OpenAI GPT models", "Groq fast inference", "Google Gemini models", "Anthropic Claude models"], "description": "AI provider to use for code assistance"}, "gocAgent.model": {"type": "string", "default": "llama-3.1-70b-versatile", "description": "AI model to use (provider-specific)"}, "gocAgent.apiKey": {"type": "string", "default": "", "description": "API key for the selected provider"}, "gocAgent.autoTraining": {"type": "boolean", "default": true, "description": "Enable automatic training and learning"}, "gocAgent.webResearch": {"type": "boolean", "default": true, "description": "Enable web research capabilities"}, "gocAgent.contextLines": {"type": "number", "default": 50, "minimum": 10, "maximum": 200, "description": "Number of context lines to include around selections"}, "gocAgent.showProgress": {"type": "boolean", "default": true, "description": "Show progress notifications for long-running operations"}, "gocAgent.autoSave": {"type": "boolean", "default": false, "description": "Automatically save files after AI modifications"}}}, "views": {"gocAgent": [{"id": "gocAgent.chatView", "name": "Cha<PERSON>", "icon": "$(comment-discussion)", "contextualTitle": "GOC Agent Cha<PERSON>"}, {"id": "gocAgent.statusView", "name": "Status", "icon": "$(pulse)", "contextualTitle": "GOC Agent Status"}, {"id": "gocAgent.historyView", "name": "History", "icon": "$(history)", "contextualTitle": "Chat History"}]}, "viewsContainers": {"activitybar": [{"id": "gocAgent", "title": "GOC Agent", "icon": "$(robot)"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "package": "vsce package", "build": "npm run compile", "dev": "npm run watch", "test": "npm run compile && node ./out/test/runTest.js", "lint": "eslint src --ext ts"}, "devDependencies": {"@types/mocha": "^10.0.1", "@types/node": "16.x", "@types/vscode": "^1.74.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "@vscode/test-electron": "^2.2.0", "eslint": "^8.28.0", "mocha": "^10.1.0", "typescript": "^4.9.4", "vsce": "^2.15.0"}, "dependencies": {"@goc-agent/core": "file:../goc-core", "@types/node-fetch": "^2.6.12", "node-fetch": "^2.7.0"}}