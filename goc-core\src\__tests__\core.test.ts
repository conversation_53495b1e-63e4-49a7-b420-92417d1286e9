import { ContextEngine } from '../context';
import { WebResearcher } from '../web';
import { logger } from '../utils';

describe('GOC Core', () => {
  describe('ContextEngine', () => {
    it('should create a context engine instance', () => {
      const contextEngine = new ContextEngine();
      expect(contextEngine).toBeInstanceOf(ContextEngine);
    });

    it('should have required methods', () => {
      const contextEngine = new ContextEngine();
      expect(typeof contextEngine.indexProject).toBe('function');
      expect(typeof contextEngine.searchContext).toBe('function');
      expect(typeof contextEngine.analyzeProject).toBe('function');
    });
  });

  describe('WebResearcher', () => {
    it('should create a web researcher instance', () => {
      const webResearcher = new WebResearcher();
      expect(webResearcher).toBeInstanceOf(WebResearcher);
    });

    it('should have required methods', () => {
      const webResearcher = new WebResearcher();
      expect(typeof webResearcher.search).toBe('function');
      expect(typeof webResearcher.researchTopic).toBe('function');
    });
  });

  describe('Logger', () => {
    it('should have logging methods', () => {
      expect(typeof logger.info).toBe('function');
      expect(typeof logger.error).toBe('function');
      expect(typeof logger.warn).toBe('function');
      expect(typeof logger.debug).toBe('function');
    });
  });
});
