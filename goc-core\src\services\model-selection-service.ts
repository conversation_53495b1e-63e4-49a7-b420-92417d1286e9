/**
 * Model Selection Service
 * 
 * Unified service for handling model selection across all GOC Agent products
 * Manages both Ollama (local) and GOC Agent (hosted) models
 */

import { AIProvider, AIModel, AIProviderConfig, BaseResult } from '../types';
import { getDefaultConfig } from '../config/default-config';

export interface ModelSelectionOptions {
  includeLocal?: boolean;
  includeHosted?: boolean;
  requiresAuth?: boolean;
  userTier?: 'free' | 'paid';
}

export interface ModelSelectionResult {
  providers: ProviderInfo[];
  recommendations: ModelRecommendations;
  userStatus?: UserStatus;
}

export interface ProviderInfo {
  name: AIProvider;
  displayName: string;
  tier: 'free' | 'freemium' | 'paid';
  isLocal: boolean;
  requiresAuth: boolean;
  isAvailable: boolean;
  models: ModelInfo[];
  setupInstructions?: string;
  authenticationUrl?: string;
}

export interface ModelInfo {
  id: string;
  name: string;
  tier: 'free' | 'paid';
  description: string;
  capabilities: string[];
  contextLength: number;
  isAvailable: boolean;
  requiresAuth: boolean;
  isLocal: boolean;
  size?: string;
  installCommand?: string;
  pricing?: {
    requestCost: number;
    currency: string;
  };
  limits?: {
    monthlyRequests: number;
    dailyRequests: number;
  };
}

export interface ModelRecommendations {
  local: string;
  cloudFree: string;
  cloudPaid: string;
}

export interface UserStatus {
  isAuthenticated: boolean;
  hasActiveSubscription: boolean;
  tier: 'free' | 'paid';
  usage?: {
    monthlyUsed: number;
    monthlyLimit: number;
    dailyUsed: number;
    dailyLimit: number;
  };
}

export class ModelSelectionService {
  private config: any;
  private ollamaAvailable: boolean = false;

  constructor() {
    this.config = getDefaultConfig();
  }

  /**
   * Get available models and providers for selection
   */
  async getModelSelection(options: ModelSelectionOptions = {}): Promise<ModelSelectionResult> {
    const {
      includeLocal = true,
      includeHosted = true,
      requiresAuth = false,
      userTier = 'free'
    } = options;

    const providers: ProviderInfo[] = [];

    // Add Ollama provider if requested
    if (includeLocal) {
      const ollamaProvider = await this.getOllamaProvider();
      providers.push(ollamaProvider);
    }

    // Add GOC Agent provider if requested
    if (includeHosted) {
      const gocProvider = await this.getGocProvider(userTier);
      providers.push(gocProvider);
    }

    const recommendations: ModelRecommendations = {
      local: 'llama3.2:3b',
      cloudFree: 'goc-agent-cloud',
      cloudPaid: 'goc-agent-dev'
    };

    return {
      providers,
      recommendations
    };
  }

  /**
   * Get Ollama provider information
   */
  private async getOllamaProvider(): Promise<ProviderInfo> {
    const ollamaConfig = this.config.ai.providers.ollama;
    const isAvailable = await this.checkOllamaAvailability();
    
    let models: ModelInfo[] = [];
    
    if (isAvailable) {
      // Get installed models from Ollama
      models = await this.getInstalledOllamaModels();
    } else {
      // Show suggested models for installation
      models = ollamaConfig.suggestedModels.map((model: any) => ({
        id: model.id,
        name: model.name,
        tier: 'free' as const,
        description: model.description,
        capabilities: model.capabilities,
        contextLength: model.contextLength,
        isAvailable: false,
        requiresAuth: false,
        isLocal: true,
        size: model.size,
        installCommand: model.installCommand
      }));
    }

    return {
      name: 'ollama',
      displayName: ollamaConfig.displayName,
      tier: 'free',
      isLocal: true,
      requiresAuth: false,
      isAvailable,
      models,
      setupInstructions: ollamaConfig.setupInstructions
    };
  }

  /**
   * Get GOC Agent provider information
   */
  private async getGocProvider(userTier: 'free' | 'paid'): Promise<ProviderInfo> {
    const gocConfig = this.config.ai.providers.goc;
    
    const models: ModelInfo[] = gocConfig.models.map((model: any) => ({
      id: model.id,
      name: model.name,
      tier: model.tier,
      description: model.description,
      capabilities: model.capabilities,
      contextLength: model.contextLength,
      isAvailable: true,
      requiresAuth: true,
      isLocal: false,
      pricing: model.pricing,
      limits: model.limits
    }));

    return {
      name: 'goc',
      displayName: gocConfig.displayName,
      tier: 'freemium',
      isLocal: false,
      requiresAuth: true,
      isAvailable: true,
      models,
      setupInstructions: gocConfig.setupInstructions,
      authenticationUrl: gocConfig.authenticationUrl
    };
  }

  /**
   * Check if Ollama is available
   */
  async checkOllamaAvailability(): Promise<boolean> {
    try {
      const response = await fetch('http://localhost:11434/api/tags', {
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      });
      this.ollamaAvailable = response.ok;
      return response.ok;
    } catch (error) {
      this.ollamaAvailable = false;
      return false;
    }
  }

  /**
   * Get installed Ollama models
   */
  async getInstalledOllamaModels(): Promise<ModelInfo[]> {
    try {
      const response = await fetch('http://localhost:11434/api/tags');
      if (!response.ok) return [];

      const data = await response.json() as any;
      const installedModels = data.models || [];

      // Map installed models to our format
      return installedModels.map((model: any) => ({
        id: model.name,
        name: this.formatModelName(model.name),
        tier: 'free' as const,
        description: this.getModelDescription(model.name),
        capabilities: this.getModelCapabilities(model.name),
        contextLength: this.getModelContextLength(model.name),
        isAvailable: true,
        requiresAuth: false,
        isLocal: true,
        size: this.formatSize(model.size)
      }));
    } catch (error) {
      return [];
    }
  }

  /**
   * Format model name for display
   */
  private formatModelName(modelId: string): string {
    const nameMap: Record<string, string> = {
      'llama3.2:3b': 'Llama 3.2 3B',
      'llama3.2:1b': 'Llama 3.2 1B',
      'codellama:7b': 'Code Llama 7B',
      'codellama:13b': 'Code Llama 13B',
      'mistral:7b': 'Mistral 7B',
      'deepseek-coder:6.7b': 'DeepSeek Coder 6.7B',
      'qwen2.5-coder:7b': 'Qwen2.5 Coder 7B'
    };
    
    return nameMap[modelId] || modelId;
  }

  /**
   * Get model description
   */
  private getModelDescription(modelId: string): string {
    const descriptionMap: Record<string, string> = {
      'llama3.2:3b': 'Fast and efficient for most coding tasks',
      'llama3.2:1b': 'Lightweight model for basic tasks',
      'codellama:7b': 'Specialized for code generation',
      'codellama:13b': 'Larger model for complex coding tasks',
      'mistral:7b': 'General purpose model',
      'deepseek-coder:6.7b': 'Optimized for coding tasks',
      'qwen2.5-coder:7b': 'Latest coding model'
    };
    
    return descriptionMap[modelId] || 'Local AI model';
  }

  /**
   * Get model capabilities
   */
  private getModelCapabilities(modelId: string): string[] {
    if (modelId.includes('code')) {
      return ['code-generation', 'code-analysis', 'code-review'];
    }
    return ['code-generation', 'general-chat'];
  }

  /**
   * Get model context length
   */
  private getModelContextLength(modelId: string): number {
    if (modelId.includes('codellama')) return 16384;
    return 32768;
  }

  /**
   * Format file size
   */
  private formatSize(bytes: number): string {
    const gb = bytes / (1024 * 1024 * 1024);
    return `${gb.toFixed(1)}GB`;
  }

  /**
   * Validate model selection
   */
  validateModelSelection(providerId: AIProvider, modelId: string): BaseResult {
    const provider = this.config.ai.providers[providerId];
    if (!provider) {
      return {
        success: false,
        error: `Provider ${providerId} not found`,
        timestamp: new Date()
      };
    }

    return {
      success: true,
      timestamp: new Date()
    };
  }
}
